from __future__ import print_function

from models import create_model
import numpy as np
import torch
import time
import trimesh
import trimesh.transformations as tra
#import surface_normal
import copy
import os
from utils import utils


class GraspEstimator:
    """
    GraspEstimator is the main class for 6DOF grasp pose estimation pipeline.
    
    Pipeline Overview:
    1. Input: Point cloud of the object
    2. Preprocessing: Normalize and prepare point cloud data
    3. Grasp Generation: Generate initial grasp poses using VAE/GAN sampler
    4. Grasp Refinement: Improve grasp poses using gradient-based or sampling-based methods
    5. Grasp Selection: Filter and select best grasps based on success probability
    6. Output: Final grasp poses with success probabilities
    
    The class integrates two neural networks:
    - Grasp Sampler: Generates diverse grasp candidates from latent space
    - Grasp Evaluator: Evaluates success probability of grasp poses
    """
    def __init__(self, grasp_sampler_opt, grasp_evaluator_opt, opt):
        """
        Initialize the GraspEstimator with model configurations.
        
        Args:
            grasp_sampler_opt: Options for grasp sampling model (VAE/GAN)
            grasp_evaluator_opt: Options for grasp evaluation model
            opt: General options containing hyperparameters
        """
        self.grasp_sampler_opt = grasp_sampler_opt
        self.grasp_evaluator_opt = grasp_evaluator_opt
        self.opt = opt
        
        # Point cloud processing parameters
        self.target_pc_size = opt.target_pc_size  # Target number of points in point cloud
        
        # Refinement parameters
        self.num_refine_steps = opt.refine_steps  # Number of iterative refinement steps
        self.refine_method = opt.refinement_method  # 'gradient' or 'sampling' based refinement
        self.threshold = opt.threshold  # Success probability threshold for grasp selection
        self.batch_size = opt.batch_size  # Batch size for processing
        
        # Grasp generation parameters
        self.generate_dense_grasps = opt.generate_dense_grasps
        if self.generate_dense_grasps:
            # Dense sampling: generate grasps on a regular grid in latent space
            self.num_grasps_per_dim = opt.num_grasp_samples
            self.num_grasp_samples = opt.num_grasp_samples * opt.num_grasp_samples
        else:
            # Random sampling: generate grasps randomly from latent space
            self.num_grasp_samples = opt.num_grasp_samples
            
        # Grasp selection strategy
        self.choose_fn = opt.choose_fn
        self.choose_fns = {
            "all": None,  # Keep all generated grasps
            "better_than_threshold": utils.choose_grasps_better_than_threshold,  # Filter by threshold
            "better_than_threshold_in_sequence": utils.choose_grasps_better_than_threshold_in_sequence,  # Sequential filtering
        }
        
        # Initialize device and models
        self.device = torch.device("cuda:0")
        self.grasp_evaluator = create_model(grasp_evaluator_opt)  # Load pretrained evaluator
        self.grasp_sampler = create_model(grasp_sampler_opt)      # Load pretrained sampler

    def keep_inliers(self, grasps, confidences, z, pc, inlier_indices_list):
        """
        Filter grasps to keep only inliers based on spatial constraints.
        
        Removes grasps that are outside reasonable spatial bounds relative to the object.
        This helps eliminate physically implausible grasp poses.
        
        Args:
            grasps: List of grasp pose batches
            confidences: List of confidence score batches
            z: List of latent code batches
            pc: List of point cloud batches
            inlier_indices_list: List of valid indices for each batch
        """
        for i, inlier_indices in enumerate(inlier_indices_list):
            grasps[i] = grasps[i][inlier_indices]
            confidences[i] = confidences[i][inlier_indices]
            z[i] = z[i][inlier_indices]
            pc[i] = pc[i][inlier_indices]

    def generate_and_refine_grasps(self, pc):
        """
        Main pipeline function that generates and refines grasp poses for input point cloud.
        
        Pipeline steps:
        1. Prepare and normalize point cloud
        2. Generate initial grasp candidates using neural sampler
        3. Filter out spatial outliers
        4. Refine grasps using iterative optimization
        5. Select best grasps based on success probability
        
        Args:
            pc (numpy.ndarray): Input point cloud of shape (N, 3)
            
        Returns:
            tuple: (grasps, success_prob)
                - grasps: Selected grasp poses as 4x4 transformation matrices
                - success_prob: Success probabilities for each selected grasp
        """
        # Step 1: Prepare point cloud data
        pc_list, pc_mean = self.prepare_pc(pc)
        
        # Step 2: Generate initial grasp candidates
        grasps_list, confidence_list, z_list = self.generate_grasps(pc_list)
        
        # Step 3: Filter spatial outliers (grasps too far from object center)
        inlier_indices = utils.get_inlier_grasp_indices(
            grasps_list,
            torch.zeros(1, 3).to(self.device),  # Object center at origin
            threshold=1.0,  # Maximum distance threshold
            device=self.device
        )
        self.keep_inliers(grasps_list, confidence_list, z_list, pc_list, inlier_indices)
        
        # Step 4: Refine grasps using iterative optimization
        improved_eulers, improved_ts, improved_success = [], [], []
        for pc, grasps in zip(pc_list, grasps_list):
            out = self.refine_grasps(pc, grasps, self.refine_method, self.num_refine_steps)
            improved_eulers.append(out[0])  # Refined euler angles
            improved_ts.append(out[1])      # Refined translations
            improved_success.append(out[2]) # Success probabilities over refinement steps
            
        # Concatenate results from all batches
        improved_eulers = np.hstack(improved_eulers)
        improved_ts = np.hstack(improved_ts)
        improved_success = np.hstack(improved_success)
        
        # Step 5: Select best grasps based on success probability
        if self.choose_fn is "all":
            selection_mask = np.ones(improved_success.shape, dtype=np.float32)
        else:
            selection_mask = self.choose_fns[self.choose_fn](
                improved_eulers, improved_ts, improved_success, self.threshold
            )
            
        # Convert selected grasps to transformation matrices
        grasps = utils.rot_and_trans_to_grasps(improved_eulers, improved_ts, selection_mask)
        
        # Denormalize grasps back to original coordinate system
        utils.denormalize_grasps(grasps, pc_mean)
        
        # Extract success probabilities for selected grasps
        refine_indexes, sample_indexes = np.where(selection_mask)
        success_prob = improved_success[refine_indexes, sample_indexes].tolist()
        
        return grasps, success_prob

    def prepare_pc(self, pc):
        """
        Preprocess point cloud for grasp generation.
        
        Steps:
        1. Downsample point cloud to target size
        2. Center point cloud at origin
        3. Replicate point cloud for batch processing
        4. Convert to torch tensors and partition into batches
        
        Args:
            pc (numpy.ndarray): Input point cloud of shape (N, 3)
            
        Returns:
            tuple: (pcs, pc_mean)
                - pcs: List of point cloud batches as torch tensors
                - pc_mean: Original centroid for denormalization
        """
        # Downsample point cloud if it's too large
        if pc.shape[0] > self.target_pc_size:
            pc = utils.regularize_pc_point_count(pc, self.target_pc_size)
            
        # Center point cloud at origin
        pc_mean = np.mean(pc, 0)
        pc -= np.expand_dims(pc_mean, 0)
        
        # Replicate point cloud for each grasp sample
        pc = np.tile(pc, (self.num_grasp_samples, 1, 1))
        pc = torch.from_numpy(pc).float().to(self.device)
        
        # pcs = []
        # Partition into batches for efficient processing
        pcs = utils.partition_array_into_subarrays(pc, self.batch_size)
        
        return pcs, pc_mean

    def generate_grasps(self, pcs):
        """
        Generate initial grasp candidates using the neural grasp sampler.
        
        Two modes:
        1. Dense sampling: Generate grasps on regular grid in latent space
        2. Random sampling: Generate grasps randomly from latent distribution
        
        Args:
            pcs: List of point cloud batches
            
        Returns:
            tuple: (all_grasps, all_confidence, all_z)
                - all_grasps: Generated grasp poses for each batch
                - all_confidence: Confidence scores from sampler
                - all_z: Latent codes used for generation
        """
        all_grasps = []
        all_confidence = []
        all_z = []
        
        if self.generate_dense_grasps:
            # Dense sampling: systematic exploration of latent space
            latent_samples = self.grasp_sampler.net.module.generate_dense_latents(
                self.num_grasps_per_dim
            )
            latent_samples = utils.partition_array_into_subarrays(latent_samples, self.batch_size)
            
            for latent_sample, pc in zip(latent_samples, pcs):
                grasps, confidence, z = self.grasp_sampler.generate_grasps(pc, latent_sample)
                all_grasps.append(grasps)
                all_confidence.append(confidence)
                all_z.append(z)
        else:
            # Random sampling: sample from learned latent distribution
            for pc in pcs:
                grasps, confidence, z = self.grasp_sampler.generate_grasps(pc)
                all_grasps.append(grasps)
                all_confidence.append(confidence)
                all_z.append(z)
                
        return all_grasps, all_confidence, all_z

    def refine_grasps(self, pc, grasps, refine_method, num_refine_steps=10):
        """
        Iteratively refine grasp poses to improve success probability.
        
        Two refinement methods:
        1. Gradient-based: Use gradients from evaluator network to optimize poses
        2. Sampling-based: Use random perturbations with acceptance/rejection
        
        Args:
            pc: Point cloud batch
            grasps: Initial grasp poses
            refine_method: 'gradient' or 'sampling'
            num_refine_steps: Number of refinement iterations
            
        Returns:
            tuple: (improved_eulers, improved_ts, improved_success)
                - improved_eulers: Euler angles over all refinement steps
                - improved_ts: Translations over all refinement steps  
                - improved_success: Success probabilities over all refinement steps
        """
        # Convert grasp poses to euler angles and translations
        grasp_eulers, grasp_translations = utils.convert_qt_to_rt(grasps)
        
        if refine_method == "gradient":
            # Gradient-based refinement: requires gradients
            improve_fun = self.improve_grasps_gradient_based
            grasp_eulers = torch.autograd.Variable(
                grasp_eulers.to(self.device), requires_grad=True
            )
            grasp_translations = torch.autograd.Variable(
                grasp_translations.to(self.device), requires_grad=True
            )
        else:
            # Sampling-based refinement: no gradients needed
            improve_fun = self.improve_grasps_sampling_based

        # Track improvements over refinement steps
        improved_success = []
        improved_eulers = []
        improved_ts = []
        
        # Store initial poses
        improved_eulers.append(grasp_eulers.cpu().data.numpy())
        improved_ts.append(grasp_translations.cpu().data.numpy())
        
        last_success = None
        
        # Iterative refinement loop
        for i in range(num_refine_steps):
            success_prob, last_success = improve_fun(
                pc, grasp_eulers, grasp_translations, last_success
            )
            improved_success.append(success_prob.cpu().data.numpy())
            improved_eulers.append(grasp_eulers.cpu().data.numpy())
            improved_ts.append(grasp_translations.cpu().data.numpy())

        # Evaluate final refined grasps
        grasp_pcs = utils.control_points_from_rot_and_trans(
            grasp_eulers, grasp_translations, self.device
        )
        improved_success.append(
            self.grasp_evaluator.evaluate_grasps(pc, grasp_pcs).squeeze().cpu().data.numpy()
        )

        return np.asarray(improved_eulers), np.asarray(improved_ts), np.asarray(improved_success)

    def improve_grasps_gradient_based(self, pcs, grasp_eulers, grasp_trans, last_success):
        """
        Improve grasp poses using gradient-based optimization.
        
        Uses backpropagation through the evaluator network to compute gradients
        of success probability w.r.t. grasp parameters, then updates poses
        in the direction of increasing success probability.
        
        Args:
            pcs: Point cloud batch
            grasp_eulers: Current euler angles (requires_grad=True)
            grasp_trans: Current translations (requires_grad=True)
            last_success: Previous success probabilities (unused)
            
        Returns:
            tuple: (success, None)
                - success: Current success probabilities
                - None: No carry-over state needed for gradient method
        """
        # Convert poses to gripper control points for evaluation
        grasp_pcs = utils.control_points_from_rot_and_trans(
            grasp_eulers, grasp_trans, self.device
        )

        # Evaluate current grasp success probability
        success = self.grasp_evaluator.evaluate_grasps(pcs, grasp_pcs)
        
        # Backpropagate to compute gradients
        success.squeeze().backward(torch.ones(success.shape[0]).to(self.device))
        
        # Update translation with adaptive step size
        delta_t = grasp_trans.grad
        norm_t = torch.norm(delta_t, p=2, dim=-1).to(self.device)
        # Limit step size to maximum 1cm to ensure gradient validity
        alpha = torch.min(0.01 / norm_t, torch.tensor(1.0).to(self.device))
        grasp_trans.data += grasp_trans.grad * alpha[:, None]
        
        # Update euler angles with same adaptive step size
        temp = grasp_eulers.clone()
        grasp_eulers.data += grasp_eulers.grad * alpha[:, None]
        
        return success.squeeze(), None

    def improve_grasps_sampling_based(self, pcs, grasp_eulers, grasp_trans, last_success=None):
        """
        Improve grasp poses using sampling-based optimization (similar to simulated annealing).
        
        Randomly perturbs current poses and accepts improvements probabilistically.
        Uses Metropolis-Hastings style acceptance criterion based on success ratio.
        
        Args:
            pcs: Point cloud batch
            grasp_eulers: Current euler angles
            grasp_trans: Current translations
            last_success: Previous success probabilities
            
        Returns:
            tuple: (last_success, next_success)
                - last_success: Previous success probabilities (returned for consistency)
                - next_success: Updated success probabilities after sampling step
        """
        with torch.no_grad():
            # Evaluate current poses if not provided
            if last_success is None:
                grasp_pcs = utils.control_points_from_rot_and_trans(
                    grasp_eulers, grasp_trans, self.device
                )
                last_success = self.grasp_evaluator.evaluate_grasps(pcs, grasp_pcs)

            # Generate random perturbations
            # Translation perturbation: ±2cm range
            delta_t = 2 * (torch.rand(grasp_trans.shape).to(self.device) - 0.5)
            delta_t *= 0.02
            
            # Euler angle perturbation: ±π range  
            delta_euler_angles = (torch.rand(grasp_eulers.shape).to(self.device) - 0.5) * 2
            
            # Apply perturbations
            perturbed_translation = grasp_trans + delta_t
            perturbed_euler_angles = grasp_eulers + delta_euler_angles
            
            # Evaluate perturbed poses
            grasp_pcs = utils.control_points_from_rot_and_trans(
                perturbed_euler_angles, perturbed_translation, self.device
            )
            perturbed_success = self.grasp_evaluator.evaluate_grasps(pcs, grasp_pcs)
            
            # Compute acceptance probability (ratio of new to old success)
            ratio = perturbed_success / torch.max(
                last_success, torch.tensor(0.0001).to(self.device)  # Avoid division by zero
            )

            # Accept improvements probabilistically (Metropolis-Hastings criterion)
            mask = torch.rand(ratio.shape).to(self.device) <= ratio

            # Update poses for accepted samples
            next_success = last_success
            ind = torch.where(mask)[0]
            next_success[ind] = perturbed_success[ind]
            grasp_trans[ind].data = perturbed_translation.data[ind]
            grasp_eulers[ind].data = perturbed_euler_angles.data[ind]
            
            return last_success.squeeze(), next_success
