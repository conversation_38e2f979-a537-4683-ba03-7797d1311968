#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Usage
------
# 整体浏览
python inspect_state_dict.py path/to/checkpoint.ckpt

# 只看某个键（支持 . 级联）
python inspect_state_dict.py path/to/ckpt -k backbone.conv1.weight

# 如果想连数值也打印
python inspect_state_dict.py path/to/ckpt -v
"""
import argparse, sys, os, torch

def locate_state_dict(obj):
    """
    根据常见约定提取“模型部分”的 state_dict：
      1. 纯 state_dict            -> 直接返回
      2. {'state_dict': …}       -> Lightning 保存
      3. {'model_state_dict': …} -> 常规 checkpoint
    找不到就抛错。
    """
    if isinstance(obj, dict):
        if "state_dict" in obj:          # Lightning
            return obj["state_dict"]
        if "model_state_dict" in obj:    # 自己封装
            return obj["model_state_dict"]
    # 纯 state_dict（直接就是参数表）
    if all(isinstance(v, torch.Tensor) for v in obj.values()):
        return obj
    raise KeyError("未找到模型 state_dict；文件结构与常见约定不一致。")

def pretty(name, tensor, show_val):
    shape = tuple(tensor.shape)
    dtype = str(tensor.dtype)
    pad   = " " * max(1, 60 - len(name))
    print(f"{name}{pad}{shape}  {dtype}")
    if show_val:
        print(tensor)

def main():
    parser = argparse.ArgumentParser("Inspect model state_dict in a PyTorch checkpoint")
    parser.add_argument("ckpt", help="path to .ckpt/.pth/.pt file")
    parser.add_argument("-k", "--key", help="print only this weight (e.g. layer1.0.conv1.weight)")
    parser.add_argument("-v", "--value", action="store_true", help="also dump tensor values")
    args = parser.parse_args()

    if not os.path.isfile(args.ckpt):
        sys.exit("文件不存在")

    obj = torch.load(args.ckpt, map_location="cpu")
    sd  = locate_state_dict(obj)

    if args.key:                               # 只看单个键
        if args.key not in sd:
            sys.exit(f"键 '{args.key}' 不在 state_dict 内")
        pretty(args.key, sd[args.key], args.value)
        return

    # 打印全部
    print(f"\nstate_dict in {args.ckpt}:")
    for k, v in sorted(sd.items()):
        pretty(k, v, args.value)

if __name__ == "__main__":
    main()
