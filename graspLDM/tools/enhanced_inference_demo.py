#!/usr/bin/env python3
"""
Demonstration of Enhanced SimpleGraspLDMInference with 6DOF-GraspNet integration.

This script shows how to use the enhanced inference interface with various
evaluation and refinement options.
"""

import os
import sys
import numpy as np
import torch

# Add the graspLDM tools directory to path
sys.path.append(os.path.dirname(__file__))

from simple_inference import SimpleGraspLDMInference, SIXDOF_AVAILABLE


def create_demo_pointcloud():
    """Create a demo point cloud (mug-like shape)."""
    # Create a cylindrical mug shape
    num_points = 2000
    height = 0.12
    radius = 0.06
    
    # Cylinder body
    theta = np.random.uniform(0, 2*np.pi, int(num_points * 0.7))
    z = np.random.uniform(0, height, int(num_points * 0.7))
    x = radius * np.cos(theta)
    y = radius * np.sin(theta)
    
    # Bottom
    theta_bottom = np.random.uniform(0, 2*np.pi, int(num_points * 0.2))
    r_bottom = np.random.uniform(0, radius, int(num_points * 0.2))
    x_bottom = r_bottom * np.cos(theta_bottom)
    y_bottom = r_bottom * np.sin(theta_bottom)
    z_bottom = np.zeros_like(x_bottom)
    
    # Handle
    handle_points = int(num_points * 0.1)
    handle_theta = np.linspace(0, np.pi, handle_points)
    handle_radius = radius * 1.3
    x_handle = handle_radius * np.cos(handle_theta) + radius
    y_handle = handle_radius * np.sin(handle_theta) * 0.3
    z_handle = np.random.uniform(height * 0.3, height * 0.7, handle_points)
    
    # Combine all parts
    x_all = np.concatenate([x, x_bottom, x_handle])
    y_all = np.concatenate([y, y_bottom, y_handle])
    z_all = np.concatenate([z, z_bottom, z_handle])
    
    return np.column_stack([x_all, y_all, z_all])


def demo_basic_usage():
    """Demonstrate basic usage without 6DOF integration."""
    print("🎯 Demo 1: Basic GraspLDM Inference")
    print("-" * 40)
    
    # Create point cloud
    pc = create_demo_pointcloud()
    print(f"Created demo point cloud: {pc.shape}")
    
    # Initialize basic inference
    inference = SimpleGraspLDMInference(
        exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        num_inference_steps=100
    )
    
    # Generate grasps
    results = inference.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=15,
        visualize=False
    )
    
    print(f"Generated {len(results['grasps'])} grasps")
    print(f"Confidence range: {results['confidence'].min():.3f} - {results['confidence'].max():.3f}")
    print(f"Mean confidence: {results['confidence'].mean():.3f}")
    
    # Filter high-confidence grasps
    good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.6)
    print(f"High-confidence grasps: {len(good_grasps['grasps'])}")
    
    return results


def demo_6dof_evaluation():
    """Demonstrate 6DOF-GraspNet evaluation integration."""
    print("\n🎯 Demo 2: 6DOF-GraspNet Evaluation")
    print("-" * 40)
    
    if not SIXDOF_AVAILABLE:
        print("⚠️  6DOF-GraspNet not available, skipping this demo")
        return None
    
    # Create point cloud
    pc = create_demo_pointcloud()
    
    # Initialize with 6DOF evaluation
    inference = SimpleGraspLDMInference(
        exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        enable_grasp_evaluation=True,
        enable_grasp_refinement=False,
        num_inference_steps=80
    )
    
    # Generate and evaluate grasps
    results = inference.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=20,
        evaluate_with_6dof=True,
        visualize=False
    )
    
    print(f"Generated {len(results['grasps'])} grasps")
    print(f"GraspLDM confidence: {results['confidence'].mean():.3f} ± {results['confidence'].std():.3f}")
    print(f"6DOF-GraspNet scores: {results['sixdof_scores'].mean():.3f} ± {results['sixdof_scores'].std():.3f}")
    
    # Compare scoring methods
    comparison = inference.compare_grasp_scores(results)
    
    # Filter by 6DOF scores
    good_6dof_grasps = inference.filter_grasps_by_6dof_score(results, min_score=0.5)
    print(f"Good 6DOF grasps: {len(good_6dof_grasps['grasps'])}")
    
    # Get top grasps by different metrics
    top_confidence = inference.get_best_grasps(results, top_k=5)
    top_6dof = inference.get_best_grasps_by_6dof(results, top_k=5)
    
    print(f"Top 5 by confidence: {top_confidence['confidence'].squeeze().tolist()}")
    print(f"Top 5 by 6DOF score: {top_6dof['sixdof_scores'].tolist()}")
    
    return results


def demo_grasp_refinement():
    """Demonstrate grasp refinement capabilities."""
    print("\n🎯 Demo 3: Grasp Refinement")
    print("-" * 40)
    
    if not SIXDOF_AVAILABLE:
        print("⚠️  6DOF-GraspNet not available, skipping this demo")
        return None
    
    # Create point cloud
    pc = create_demo_pointcloud()
    
    # Test both refinement methods
    for method in ["gradient", "sampling"]:
        print(f"\n  Testing {method}-based refinement:")
        
        # Initialize with refinement
        inference = SimpleGraspLDMInference(
            exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            enable_grasp_evaluation=True,
            enable_grasp_refinement=True,
            refinement_method=method,
            refinement_steps=5,
            num_inference_steps=60
        )
        
        # Generate and refine grasps
        results = inference.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=10,
            refine_grasps=True,
            visualize=False
        )
        
        # Compare before and after refinement
        initial_scores = results['sixdof_scores']
        refined_scores = results['refined_scores']
        
        improvement = refined_scores.mean() - initial_scores.mean()
        print(f"    Initial score: {initial_scores.mean():.3f}")
        print(f"    Refined score: {refined_scores.mean():.3f}")
        print(f"    Improvement: {improvement:+.3f}")
        
        # Count improved grasps
        improved_count = (refined_scores > initial_scores).sum().item()
        print(f"    Improved grasps: {improved_count}/{len(refined_scores)}")
    
    return results


def demo_advanced_features():
    """Demonstrate advanced features and customization."""
    print("\n🎯 Demo 4: Advanced Features")
    print("-" * 40)
    
    # Create point cloud
    pc = create_demo_pointcloud()
    
    # Initialize with full features
    inference = SimpleGraspLDMInference(
        exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        enable_grasp_evaluation=SIXDOF_AVAILABLE,
        enable_grasp_refinement=SIXDOF_AVAILABLE,
        refinement_method="gradient",
        refinement_steps=3,
        num_inference_steps=50
    )
    
    # Generate grasps with intermediate steps
    results = inference.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=12,
        return_intermediate=True,
        evaluate_with_6dof=SIXDOF_AVAILABLE,
        refine_grasps=SIXDOF_AVAILABLE,
        visualize=False
    )
    
    print(f"Generated {len(results['grasps'])} grasps")
    
    if results.get('all_steps_grasps'):
        print(f"Captured {len(results['all_steps_grasps'])} diffusion steps")
    
    if SIXDOF_AVAILABLE and 'sixdof_scores' in results:
        print(f"6DOF evaluation completed")
        print(f"Score distribution: min={results['sixdof_scores'].min():.3f}, "
              f"max={results['sixdof_scores'].max():.3f}")
    
    if SIXDOF_AVAILABLE and 'refined_grasps' in results:
        print(f"Grasp refinement completed")
    
    # Save visualization
    save_path = "demo_grasps_enhanced.glb"
    success = inference.save_visualization(results, save_path)
    if success:
        print(f"Visualization saved to: {save_path}")
    
    return results


def demo_camera_pose_integration():
    """Demonstrate camera pose transformation."""
    print("\n🎯 Demo 5: Camera Pose Integration")
    print("-" * 40)
    
    # Create point cloud in camera coordinates
    pc_camera = create_demo_pointcloud()
    
    # Create a camera pose transformation (camera to world)
    camera_pose = np.eye(4)
    camera_pose[:3, 3] = [0.2, 0.1, 0.3]  # Camera translation
    camera_pose[:3, :3] = np.array([  # Camera rotation (45 degrees around Z)
        [0.707, -0.707, 0],
        [0.707,  0.707, 0],
        [0,      0,     1]
    ])
    
    print(f"Camera pose transformation:")
    print(camera_pose)
    
    # Initialize inference
    inference = SimpleGraspLDMInference(
        exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        enable_grasp_evaluation=SIXDOF_AVAILABLE,
        num_inference_steps=40
    )
    
    # Generate grasps with camera pose transformation
    results = inference.infer_from_pointcloud(
        pointcloud=pc_camera,
        camera_pose=camera_pose,
        num_grasps=8,
        evaluate_with_6dof=SIXDOF_AVAILABLE,
        visualize=False
    )
    
    print(f"Generated {len(results['grasps'])} grasps with camera pose transformation")
    print(f"Grasp positions (world coordinates):")
    for i, grasp in enumerate(results['grasps'][:3]):  # Show first 3
        pos = grasp[:3, 3].numpy()
        print(f"  Grasp {i+1}: [{pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}]")
    
    return results


def main():
    """Run all demonstrations."""
    print("🚀 Enhanced SimpleGraspLDMInference Demonstration")
    print("=" * 60)
    
    # Check if model path exists
    model_path = "checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k"
    if not os.path.exists(model_path):
        print(f"❌ Model path not found: {model_path}")
        print("Please ensure you have downloaded the GraspLDM model checkpoints.")
        return 1
    
    print(f"6DOF-GraspNet integration: {'✅ Available' if SIXDOF_AVAILABLE else '❌ Not available'}")
    print()
    
    try:
        # Run demonstrations
        demo_basic_usage()
        demo_6dof_evaluation()
        demo_grasp_refinement()
        demo_advanced_features()
        demo_camera_pose_integration()
        
        print("\n" + "=" * 60)
        print("🎉 All demonstrations completed successfully!")
        print("\nKey Features Demonstrated:")
        print("  ✅ Basic GraspLDM inference")
        print("  ✅ 6DOF-GraspNet evaluation integration")
        print("  ✅ Gradient and sampling-based refinement")
        print("  ✅ Advanced filtering and comparison utilities")
        print("  ✅ Camera pose transformation support")
        print("  ✅ Backward compatibility")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
