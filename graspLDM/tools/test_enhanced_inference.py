#!/usr/bin/env python3
"""
Test script for Enhanced SimpleGraspLDMInference with 6DOF-GraspNet integration.

This script validates the integration functionality and ensures backward compatibility.
"""

import os
import sys
import numpy as np
import torch
import warnings

# Add the graspLDM tools directory to path
sys.path.append(os.path.dirname(__file__))

try:
    from simple_inference import SimpleGraspLDMInference, SIXDOF_AVAILABLE
    print(f"✓ Successfully imported SimpleGraspLDMInference")
    print(f"  6DOF-GraspNet available: {SIXDOF_AVAILABLE}")
except ImportError as e:
    print(f"❌ Failed to import SimpleGraspLDMInference: {e}")
    sys.exit(1)


def create_test_pointcloud(shape="sphere", num_points=2000, radius=0.15):
    """Create synthetic point clouds for testing."""
    if shape == "sphere":
        # Generate sphere point cloud
        theta = np.random.uniform(0, 2*np.pi, num_points)
        phi = np.random.uniform(0, np.pi, num_points)
        
        x = radius * np.sin(phi) * np.cos(theta)
        y = radius * np.sin(phi) * np.sin(theta)
        z = radius * np.cos(phi)
        
        return np.column_stack([x, y, z])
    
    elif shape == "cube":
        # Generate cube point cloud
        points = []
        points_per_face = num_points // 6
        
        for face in range(6):
            if face == 0:  # +X face
                x = np.full(points_per_face, radius)
                y = np.random.uniform(-radius, radius, points_per_face)
                z = np.random.uniform(-radius, radius, points_per_face)
            elif face == 1:  # -X face
                x = np.full(points_per_face, -radius)
                y = np.random.uniform(-radius, radius, points_per_face)
                z = np.random.uniform(-radius, radius, points_per_face)
            elif face == 2:  # +Y face
                x = np.random.uniform(-radius, radius, points_per_face)
                y = np.full(points_per_face, radius)
                z = np.random.uniform(-radius, radius, points_per_face)
            elif face == 3:  # -Y face
                x = np.random.uniform(-radius, radius, points_per_face)
                y = np.full(points_per_face, -radius)
                z = np.random.uniform(-radius, radius, points_per_face)
            elif face == 4:  # +Z face
                x = np.random.uniform(-radius, radius, points_per_face)
                y = np.random.uniform(-radius, radius, points_per_face)
                z = np.full(points_per_face, radius)
            else:  # -Z face
                x = np.random.uniform(-radius, radius, points_per_face)
                y = np.random.uniform(-radius, radius, points_per_face)
                z = np.full(points_per_face, -radius)
            
            points.extend(zip(x, y, z))
        
        return np.array(points)
    
    else:
        raise ValueError(f"Unknown shape: {shape}")


def test_basic_functionality():
    """Test basic graspLDM functionality without 6DOF integration."""
    print("🧪 Testing basic graspLDM functionality...")
    
    # Create test point cloud
    pc = create_test_pointcloud("sphere", 1500, 0.12)
    print(f"  Created test point cloud: {pc.shape}")
    
    # Test with minimal configuration (6DOF features disabled)
    try:
        inference = SimpleGraspLDMInference(
            exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            enable_grasp_evaluation=False,
            enable_grasp_refinement=False,
            num_inference_steps=50  # Faster for testing
        )
        
        # Basic inference
        results = inference.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=10,
            visualize=False
        )
        
        # Validate results structure
        assert "grasps" in results, "Missing 'grasps' in results"
        assert "confidence" in results, "Missing 'confidence' in results"
        assert "pc" in results, "Missing 'pc' in results"
        assert results["grasps"].shape[0] == 10, f"Expected 10 grasps, got {results['grasps'].shape[0]}"
        assert results["grasps"].shape[1:] == (4, 4), f"Expected [N, 4, 4] grasps, got {results['grasps'].shape}"
        
        print("  ✅ Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Basic functionality test failed: {e}")
        return False


def test_6dof_integration():
    """Test 6DOF-GraspNet integration features."""
    print("🧪 Testing 6DOF-GraspNet integration...")
    
    if not SIXDOF_AVAILABLE:
        print("  ⚠️  6DOF-GraspNet not available, skipping integration tests")
        return True
    
    # Create test point cloud
    pc = create_test_pointcloud("cube", 1200, 0.10)
    print(f"  Created test point cloud: {pc.shape}")
    
    try:
        # Test with 6DOF evaluation enabled
        inference = SimpleGraspLDMInference(
            exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            enable_grasp_evaluation=True,
            enable_grasp_refinement=False,
            num_inference_steps=30  # Faster for testing
        )
        
        # Test evaluation only
        results = inference.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=8,
            evaluate_with_6dof=True,
            visualize=False
        )
        
        # Validate 6DOF evaluation results
        assert "sixdof_scores" in results, "Missing '6DOF scores' in results"
        assert results["sixdof_scores"].shape[0] == 8, f"Expected 8 6DOF scores, got {results['sixdof_scores'].shape[0]}"
        
        print("  ✅ 6DOF evaluation test passed")
        
        # Test refinement
        inference_refine = SimpleGraspLDMInference(
            exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            enable_grasp_evaluation=True,
            enable_grasp_refinement=True,
            refinement_method="sampling",  # Use sampling for faster testing
            refinement_steps=3,
            num_inference_steps=30
        )
        
        results_refined = inference_refine.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=6,
            refine_grasps=True,
            visualize=False
        )
        
        # Validate refinement results
        assert "refined_grasps" in results_refined, "Missing 'refined_grasps' in results"
        assert "refined_scores" in results_refined, "Missing 'refined_scores' in results"
        assert results_refined["refined_grasps"].shape[0] == 6, f"Expected 6 refined grasps"
        
        print("  ✅ 6DOF refinement test passed")
        return True
        
    except Exception as e:
        print(f"  ❌ 6DOF integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_utility_functions():
    """Test utility functions for grasp filtering and comparison."""
    print("🧪 Testing utility functions...")
    
    try:
        # Create mock results for testing
        num_grasps = 12
        mock_results = {
            "grasps": torch.randn(num_grasps, 4, 4),
            "grasp_tmrp": torch.randn(num_grasps, 6),
            "confidence": torch.rand(num_grasps, 1),
            "pc": torch.randn(1024, 3),
            "sixdof_scores": torch.rand(num_grasps)
        }
        
        # Create inference instance for testing utilities
        inference = SimpleGraspLDMInference(
            exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
            device="cpu",
            enable_grasp_evaluation=False,
            enable_grasp_refinement=False
        )
        
        # Test confidence filtering
        filtered = inference.filter_grasps_by_confidence(mock_results, min_confidence=0.3)
        assert len(filtered["grasps"]) <= num_grasps, "Filtering should reduce or maintain grasp count"
        
        # Test 6DOF score filtering
        filtered_6dof = inference.filter_grasps_by_6dof_score(mock_results, min_score=0.4)
        assert len(filtered_6dof["grasps"]) <= num_grasps, "6DOF filtering should reduce or maintain grasp count"
        
        # Test top-k selection
        top_grasps = inference.get_best_grasps(mock_results, top_k=5)
        assert len(top_grasps["grasps"]) == 5, "Top-k should return exactly k grasps"
        
        # Test 6DOF top-k selection
        top_6dof = inference.get_best_grasps_by_6dof(mock_results, top_k=4)
        assert len(top_6dof["grasps"]) == 4, "6DOF top-k should return exactly k grasps"
        
        # Test score comparison
        comparison = inference.compare_grasp_scores(mock_results)
        assert "correlation" in comparison, "Comparison should include correlation"
        assert "agreement_rate" in comparison, "Comparison should include agreement rate"
        
        print("  ✅ Utility functions test passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Utility functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backward_compatibility():
    """Test that existing code still works with the enhanced interface."""
    print("🧪 Testing backward compatibility...")
    
    try:
        # Test original interface without new parameters
        pc = create_test_pointcloud("sphere", 800, 0.08)
        
        inference = SimpleGraspLDMInference(
            exp_path="checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k",
            device="cpu",
            num_inference_steps=20
        )
        
        # Original method call should work unchanged
        results = inference.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=5,
            visualize=False
        )
        
        # Should have original result structure
        expected_keys = {"grasps", "grasp_tmrp", "confidence", "pc"}
        assert expected_keys.issubset(set(results.keys())), "Missing expected keys in results"
        
        print("  ✅ Backward compatibility test passed")
        return True
        
    except Exception as e:
        print(f"  ❌ Backward compatibility test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Enhanced SimpleGraspLDMInference Tests")
    print("=" * 60)
    
    # Suppress warnings for cleaner output
    warnings.filterwarnings("ignore")
    
    test_results = []
    
    # Run tests
    test_results.append(("Basic Functionality", test_basic_functionality()))
    test_results.append(("6DOF Integration", test_6dof_integration()))
    test_results.append(("Utility Functions", test_utility_functions()))
    test_results.append(("Backward Compatibility", test_backward_compatibility()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
