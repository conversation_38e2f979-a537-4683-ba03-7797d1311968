#!/usr/bin/env python3
"""
Quick test script to verify the fixes to simple_inference.py
"""

import os
import sys
import numpy as np
import torch

# Add the graspLDM tools directory to path
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """Test that all imports work correctly."""
    print("🧪 Testing imports...")
    
    try:
        from simple_inference import SimpleGraspLDMInference, SIXDOF_AVAILABLE
        print(f"  ✅ SimpleGraspLDMInference imported successfully")
        print(f"  6DOF-GraspNet available: {'✅ Yes' if SIXDOF_AVAILABLE else '❌ No'}")
        return True, SimpleGraspLDMInference, SIXDOF_AVAILABLE
    except ImportError as e:
        print(f"  ❌ Import failed: {e}")
        return False, None, False

def test_6dof_utils():
    """Test that 6DOF utils can be imported and used."""
    print("\n🧪 Testing 6DOF utils...")
    
    if not SIXDOF_AVAILABLE:
        print("  ⚠️  6DOF-GraspNet not available, skipping utils test")
        return True
    
    try:
        # Test importing utils
        sixdof_path = os.path.join(os.path.dirname(__file__), '../6dof_evaluator')
        sys.path.append(sixdof_path)
        from utils import utils as grasp_utils
        
        print(f"  ✅ 6DOF utils imported successfully")
        
        # Test key functions exist
        required_functions = [
            'control_points_from_rot_and_trans',
            'convert_qt_to_rt',
            'tc_rotation_matrix',
            'get_control_point_tensor'
        ]
        
        for func_name in required_functions:
            if hasattr(grasp_utils, func_name):
                print(f"    ✅ {func_name} available")
            else:
                print(f"    ❌ {func_name} missing")
                return False
        
        # Test control_points_from_rot_and_trans function
        test_eulers = torch.randn(3, 3)  # 3 grasps, 3 euler angles each
        test_translations = torch.randn(3, 3)  # 3 grasps, 3D translations
        
        control_points = grasp_utils.control_points_from_rot_and_trans(
            test_eulers, test_translations, device="cpu"
        )
        
        print(f"    ✅ control_points_from_rot_and_trans works: {control_points.shape}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 6DOF utils test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_initialization():
    """Test basic initialization without 6DOF features."""
    print("\n🧪 Testing basic initialization...")
    
    try:
        # Test with minimal configuration
        inference = SimpleGraspLDMInference(
            exp_path="dummy_path",  # This will fail but we're testing initialization
            device="cpu",
            enable_grasp_evaluation=False,
            enable_grasp_refinement=False
        )
        
        print(f"  ✅ Basic initialization successful")
        print(f"    6DOF evaluation: {inference.enable_grasp_evaluation}")
        print(f"    6DOF refinement: {inference.enable_grasp_refinement}")
        
        return True
        
    except Exception as e:
        # Expected to fail due to dummy path, but should get past initialization
        if "dummy_path" in str(e) or "experiment" in str(e).lower():
            print(f"  ✅ Basic initialization successful (expected path error)")
            return True
        else:
            print(f"  ❌ Unexpected initialization error: {e}")
            return False

def test_6dof_initialization():
    """Test 6DOF initialization."""
    print("\n🧪 Testing 6DOF initialization...")
    
    if not SIXDOF_AVAILABLE:
        print("  ⚠️  6DOF-GraspNet not available, skipping 6DOF initialization test")
        return True
    
    try:
        # Test with 6DOF features enabled
        inference = SimpleGraspLDMInference(
            exp_path="dummy_path",
            device="cpu",
            enable_grasp_evaluation=True,
            enable_grasp_refinement=True,
            refinement_method="sampling",
            refinement_steps=3
        )
        
        print(f"  ✅ 6DOF initialization successful")
        print(f"    6DOF evaluation: {inference.enable_grasp_evaluation}")
        print(f"    6DOF refinement: {inference.enable_grasp_refinement}")
        print(f"    Refinement method: {inference.refinement_method}")
        
        return True
        
    except Exception as e:
        # Expected to fail due to dummy path, but should get past 6DOF setup
        if "dummy_path" in str(e) or "experiment" in str(e).lower():
            print(f"  ✅ 6DOF initialization successful (expected path error)")
            return True
        else:
            print(f"  ❌ Unexpected 6DOF initialization error: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_format_converter():
    """Test the GraspFormatConverter class."""
    print("\n🧪 Testing GraspFormatConverter...")
    
    try:
        from simple_inference import GraspFormatConverter
        
        converter = GraspFormatConverter()
        
        # Test TMRP to 6DOF conversion
        test_tmrp = torch.randn(5, 6)  # 5 grasps in TMRP format
        qt_grasps = converter.tmrp_to_6dof_format(test_tmrp)
        
        print(f"  ✅ TMRP to 6DOF conversion: {test_tmrp.shape} -> {qt_grasps.shape}")
        
        # Test homogeneous to 6DOF conversion
        test_H = torch.eye(4).unsqueeze(0).repeat(3, 1, 1)  # 3 identity matrices
        qt_grasps_H = converter.homogeneous_to_6dof_format(test_H)
        
        print(f"  ✅ Homogeneous to 6DOF conversion: {test_H.shape} -> {qt_grasps_H.shape}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ GraspFormatConverter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Enhanced SimpleGraspLDMInference Fixes")
    print("=" * 60)
    
    global SIXDOF_AVAILABLE
    
    # Test imports
    success, SimpleGraspLDMInference, SIXDOF_AVAILABLE = test_imports()
    if not success:
        print("\n❌ Import test failed, cannot continue")
        return 1
    
    # Run tests
    tests = [
        ("6DOF Utils", test_6dof_utils),
        ("Basic Initialization", test_basic_initialization),
        ("6DOF Initialization", test_6dof_initialization),
        ("Format Converter", test_format_converter),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
