# graspLDM 自定义点云推理指南

本指南详细说明如何在您自己的点云数据上使用graspLDM进行抓取姿态推理。

## 目录
1. [准备工作](#准备工作)
2. [快速开始](#快速开始)
3. [详细使用说明](#详细使用说明)
4. [数据格式要求](#数据格式要求)
5. [高级用法](#高级用法)
6. [常见问题](#常见问题)

## 准备工作

### 1. 确保模型已训练
首先确认您已经有训练好的graspLDM模型，检查点文件应位于：
```
output/YOUR_EXP_NAME/
├── checkpoints/
│   ├── vae/
│   │   └── last.ckpt
│   └── ddm/
│       └── last.ckpt
└── configs/
    └── config.yaml
```

### 2. 依赖检查
确保安装了必要的依赖：
```bash
pip install trimesh torch numpy
```

## 快速开始

### 方法1：使用命令行脚本（推荐）

```bash
# 基本用法：对自己的点云进行无条件抓取生成
python tools/inference_custom_pointcloud.py \
    --pointcloud_path /path/to/your/pointcloud.npy \
    --exp_name your_experiment_name \
    --mode LDM \
    --num_grasps 20 \
    --visualize

# 高级用法：使用类别条件生成
python tools/inference_custom_pointcloud.py \
    --pointcloud_path /path/to/your/pointcloud.npy \
    --exp_name your_experiment_name \
    --mode LDM \
    --num_grasps 20 \
    --condition_type CLASS_CONDITIONED \
    --class_label 0 \
    --min_confidence 0.5 \
    --visualize \
    --save_results results.ply
```

### 方法2：在Python代码中使用

```python
from tools.inference_custom_pointcloud import CustomPointCloudInference, Conditioning
import numpy as np
import torch

# 加载您的点云数据
pointcloud = np.load("your_pointcloud.npy")  # shape: [N, 3]

# 初始化推理器
inference = CustomPointCloudInference(
    exp_name="your_experiment_name",
    mode="LDM"  # 或 "VAE"
)

# 预处理点云
pc_tensor = torch.from_numpy(pointcloud).float()
pc_processed, stats = inference.preprocess_pointcloud(pc_tensor)

# 运行推理
results = inference.infer_grasps(
    pc_processed,
    num_grasps=15,
    condition_type=Conditioning.UNCONDITIONAL
)

# 获取结果
grasps = results['grasps']        # [N, 4, 4] 抓取变换矩阵
confidence = results['confidence'] # [N, 1] 置信度分数
pointcloud = results['pointcloud'] # [M, 3] 去归一化的点云
```

## 详细使用说明

### 数据预处理

graspLDM需要特定格式的点云输入：

1. **点数量**：推荐1024个点（会自动采样调整）
2. **坐标范围**：模型在归一化数据上训练，脚本会自动处理
3. **中心化**：点云会自动中心化到质心
4. **去除异常值**：自动移除距离中心过远的点

### 推理模式对比

| 特性 | VAE模式 | LDM模式 |
|-----|---------|---------|
| 生成速度 | 快（一次前向传播） | 慢（多步去噪过程） |
| 生成质量 | 较好 | 更好 |
| 条件生成 | 仅支持无条件 | 支持所有条件类型 |
| 多样性 | 中等 | 高 |

### 条件生成类型

#### 1. 无条件生成（UNCONDITIONAL）
```python
results = inference.infer_grasps(
    pc_processed,
    condition_type=Conditioning.UNCONDITIONAL
)
```
- 适用：探索性抓取，不确定物体类型时
- 特点：最大随机性和多样性

#### 2. 类别条件生成（CLASS_CONDITIONED）- 仅LDM
```python
results = inference.infer_grasps(
    pc_processed,
    condition_type=Conditioning.CLASS_CONDITIONED,
    class_label=0  # 0=Cup, 1=Bowl, 2=Bottle等
)
```
- 适用：已知物体类别时
- 特点：生成针对特定物体类型的抓取策略

#### 3. 区域条件生成（REGION_CONDITIONED）- 仅LDM
```python
# 需要手动指定感兴趣区域
results = inference.infer_grasps(
    pc_processed,
    condition_type=Conditioning.REGION_CONDITIONED,
    region_id=0
)
```
- 适用：指定抓取区域时
- 特点：在特定空间区域内生成抓取

## 数据格式要求

### 支持的文件格式

| 格式 | 描述 | 加载方式 |
|-----|------|----------|
| `.npy` | NumPy数组 | `np.load()` |
| `.npz` | 压缩NumPy | `np.load()['points']` |
| `.txt` | 文本文件 | `np.loadtxt()` |
| `.ply` | 点云文件 | `trimesh.load()` + 采样 |
| `.obj` | 网格文件 | `trimesh.load()` + 采样 |
| `.stl` | 网格文件 | `trimesh.load()` + 采样 |

### 数据格式规范

```python
# 期望的点云格式
pointcloud.shape  # 应该是 [N, 3]，其中N是点数量
pointcloud.dtype  # 应该是 float32 或 float64

# 坐标系约定
# X: 右（红色轴）
# Y: 前（绿色轴）  
# Z: 上（蓝色轴）
```

### 数据质量要求

1. **完整性**：点云应该覆盖物体的主要表面
2. **密度**：点云密度应该相对均匀
3. **噪声**：避免过多的噪声点
4. **尺度**：物体尺寸应该在合理范围内（几厘米到几十厘米）

## 高级用法

### 1. 批处理多个点云

```python
import glob

pointcloud_files = glob.glob("data/*.npy")
for pc_file in pointcloud_files:
    print(f"Processing {pc_file}...")
    # 加载和处理逻辑
    results = inference.infer_grasps(pc_processed)
    # 保存结果
```

### 2. 结果后处理

```python
# 按置信度排序
sorted_indices = torch.argsort(results['confidence'].squeeze(), descending=True)
best_grasps = results['grasps'][sorted_indices[:5]]  # 取前5个最佳抓取

# 过滤重复抓取（基于位置距离）
def filter_duplicate_grasps(grasps, min_distance=0.02):
    positions = grasps[:, :3, 3]  # 提取位置
    filtered_indices = []
    for i, pos in enumerate(positions):
        if len(filtered_indices) == 0:
            filtered_indices.append(i)
        else:
            distances = torch.norm(positions[filtered_indices] - pos, dim=1)
            if torch.min(distances) > min_distance:
                filtered_indices.append(i)
    return grasps[filtered_indices]
```

### 3. 坐标系转换

```python
# 如果您的机器人使用不同的坐标系
def transform_grasps_to_robot_frame(grasps, transform_matrix):
    """
    将抓取姿态从模型坐标系转换到机器人坐标系
    
    Args:
        grasps: [N, 4, 4] 抓取变换矩阵
        transform_matrix: [4, 4] 坐标系变换矩阵
    """
    robot_grasps = torch.matmul(transform_matrix, grasps)
    return robot_grasps
```

### 4. 与ROS集成

```python
import rospy
from geometry_msgs.msg import PoseStamped
from tf.transformations import quaternion_from_matrix

def grasps_to_ros_poses(grasps, frame_id="base_link"):
    """将抓取矩阵转换为ROS PoseStamped消息"""
    poses = []
    for grasp in grasps:
        pose = PoseStamped()
        pose.header.frame_id = frame_id
        pose.header.stamp = rospy.Time.now()
        
        # 位置
        pose.pose.position.x = grasp[0, 3].item()
        pose.pose.position.y = grasp[1, 3].item() 
        pose.pose.position.z = grasp[2, 3].item()
        
        # 方向（四元数）
        rotation_matrix = grasp[:3, :3].numpy()
        quaternion = quaternion_from_matrix(grasp.numpy())
        pose.pose.orientation.x = quaternion[0]
        pose.pose.orientation.y = quaternion[1]
        pose.pose.orientation.z = quaternion[2]
        pose.pose.orientation.w = quaternion[3]
        
        poses.append(pose)
    
    return poses
```

## 常见问题

### Q1: 模型加载失败
**错误**: `FileNotFoundError: Model checkpoint not found`

**解决**:
- 检查实验名称是否正确
- 确认检查点文件路径：`output/YOUR_EXP_NAME/checkpoints/`
- 确保VAE和DDM检查点都存在

### Q2: 内存不足
**错误**: `CUDA out of memory`

**解决**:
```python
# 减少点云数量
pc_processed, _ = inference.preprocess_pointcloud(pc_tensor, target_num_points=512)

# 减少生成的抓取数量
results = inference.infer_grasps(pc_processed, num_grasps=5)

# 使用CPU
inference = CustomPointCloudInference(device="cpu")
```

### Q3: 生成的抓取质量差
**可能原因和解决方案**:

1. **点云质量问题**
   - 确保点云覆盖完整
   - 移除噪声点和异常值
   - 检查点云密度

2. **尺度问题**
   ```python
   # 手动调整点云尺度
   pointcloud = pointcloud * scale_factor  # 放大或缩小
   ```

3. **坐标系问题**
   - 确保Z轴朝上
   - 检查抓取器方向定义

### Q4: 可视化失败
**解决**:
```python
# 保存为文件进行外部查看
inference._save_results_as_mesh(results, "results.ply")

# 或使用其他可视化工具
import open3d as o3d
pcd = o3d.geometry.PointCloud()
pcd.points = o3d.utility.Vector3dVector(pointcloud.numpy())
o3d.visualization.draw_geometries([pcd])
```

### Q5: 条件生成无效果
**检查**:
- 确保使用LDM模式（VAE不支持条件生成）
- 验证类别标签范围（通常0-62）
- 检查模型是否使用条件训练

## 性能优化建议

1. **批处理**: 一次处理多个点云提高效率
2. **GPU使用**: 确保使用GPU加速
3. **点云采样**: 使用适当的点数量（1024通常是最优的）
4. **预处理缓存**: 对重复使用的点云缓存预处理结果

## 进一步开发

1. **自定义后处理**: 根据具体应用添加抓取过滤逻辑
2. **多模态输入**: 结合RGB图像和深度信息
3. **在线学习**: 根据执行反馈微调模型
4. **硬件集成**: 与实际机器人系统集成测试

---

希望这个指南能帮助您成功地在自己的点云数据上使用graspLDM！如有问题，请查看代码注释或联系开发团队。 