"""
Enhanced GraspLDM Inference Interface with 6DOF-GraspNet Integration

This module provides a streamlined inference interface for GraspLDM that bypasses
the complex dataset loading pipeline and accepts raw point cloud data directly.
It also integrates 6DOF-GraspNet's grasp evaluation and refinement capabilities.

Key Features:
- Direct point cloud input (numpy arrays or torch tensors)
- Camera extrinsic parameter support
- Same preprocessing pipeline as original implementation
- Compatible with existing model weights and configurations
- Integrated 6DOF-GraspNet grasp evaluator for success probability scoring
- Grasp refinement using gradient-based and sampling-based optimization
- Suitable for real-time robotic applications

Usage Example:
    Basic inference:
    >>> inference = SimpleGraspLDMInference(exp_path="path/to/experiment")
    >>> results = inference.infer_from_pointcloud(pointcloud_array, num_grasps=20)

    With 6DOF-GraspNet evaluation and refinement:
    >>> inference = SimpleGraspLDMInference(
    ...     exp_path="path/to/experiment",
    ...     enable_grasp_evaluation=True,
    ...     enable_grasp_refinement=True,
    ...     evaluator_model_path="path/to/6dof_evaluator.pth"
    ... )
    >>> results = inference.infer_from_pointcloud(
    ...     pointcloud=pointcloud_array,
    ...     num_grasps=20,
    ...     refine_grasps=True,
    ...     refinement_steps=5
    ... )

    With visualization and saving:
    >>> results = inference.infer_from_pointcloud(
    ...     pointcloud=pointcloud_array,
    ...     num_grasps=20,
    ...     visualize=True,
    ...     save_path="grasps_visualization.glb"  # Supports .glb, .ply, .obj, .stl
    ... )

    # Generate grasps from point cloud
    results = inference.infer_from_pointcloud(
        pointcloud=pc_array,  # [N, 3] numpy array or torch tensor
        camera_pose=cam_pose, # [4, 4] camera extrinsic matrix (optional)
        num_grasps=20,
        visualize=True
    )
"""

import os
import warnings
from typing import Union, Optional, Tuple, Dict, Any

import numpy as np
import torch
import torch.nn as nn

from grasp_ldm.models.builder import build_model_from_cfg
from grasp_ldm.utils.config import Config
from grasp_ldm.utils.rotations import tmrp_to_H, H_to_tmrp
from grasp_ldm.utils.pointcloud_helpers import PointCloudHelpers
from grasp_ldm.utils.vis import visualize_pc_grasps
from tools.inference import Experiment, fix_state_dict_prefix, unnormalize_grasps, unnormalize_pc

# Import 6DOF-GraspNet components for evaluation and refinement
try:
    import sys
    # Add the 6dof_evaluator directory to path
    sixdof_path = os.path.join(os.path.dirname(__file__), '../6dof_evaluator')
    sys.path.append(sixdof_path)
    from models import create_model as create_6dof_model
    from utils import utils as grasp_utils
    SIXDOF_AVAILABLE = True
    print(f"✓ 6DOF-GraspNet loaded from: {sixdof_path}")
except ImportError as e:
    print(f"⚠️  6DOF-GraspNet not available: {e}")
    print("   Grasp evaluation and refinement features will be disabled")
    SIXDOF_AVAILABLE = False


class GraspFormatConverter:
    """
    Utility class for converting between graspLDM and 6DOF-GraspNet data formats.

    Handles conversions between:
    - TMRP (Translation + Modified Rodrigues Parameters) ↔ Homogeneous matrices
    - graspLDM coordinate system ↔ 6DOF-GraspNet coordinate system
    - Point cloud normalization/denormalization
    """

    @staticmethod
    def tmrp_to_6dof_format(tmrp_grasps: torch.Tensor) -> torch.Tensor:
        """
        Convert TMRP format grasps to 6DOF-GraspNet quaternion+translation format.

        Args:
            tmrp_grasps: [N, 6] TMRP format (translation + MRP)

        Returns:
            torch.Tensor: [N, 7] quaternion+translation format (qw, qx, qy, qz, tx, ty, tz)
        """
        # Convert TMRP to homogeneous matrices
        H_matrices = tmrp_to_H(tmrp_grasps)  # [N, 4, 4]

        # Extract rotation matrices and translations
        R = H_matrices[..., :3, :3]  # [N, 3, 3]
        t = H_matrices[..., :3, 3]   # [N, 3]

        # Convert rotation matrices to quaternions (wxyz format)
        from grasp_ldm.utils.rotations import rotmat_to_quat
        quaternions = rotmat_to_quat(R, return_wxyz=True)  # [N, 4] (w, x, y, z)

        # Concatenate quaternions and translations
        qt_grasps = torch.cat([quaternions, t], dim=-1)  # [N, 7]

        return qt_grasps

    @staticmethod
    def homogeneous_to_6dof_format(H_grasps: torch.Tensor) -> torch.Tensor:
        """
        Convert homogeneous transformation matrices to 6DOF-GraspNet format.

        Args:
            H_grasps: [N, 4, 4] homogeneous transformation matrices

        Returns:
            torch.Tensor: [N, 7] quaternion+translation format
        """
        # Extract rotation matrices and translations
        R = H_grasps[..., :3, :3]  # [N, 3, 3]
        t = H_grasps[..., :3, 3]   # [N, 3]

        # Convert rotation matrices to quaternions (wxyz format)
        from grasp_ldm.utils.rotations import rotmat_to_quat
        quaternions = rotmat_to_quat(R, return_wxyz=True)  # [N, 4] (w, x, y, z)

        # Concatenate quaternions and translations
        qt_grasps = torch.cat([quaternions, t], dim=-1)  # [N, 7]

        return qt_grasps

    @staticmethod
    def normalize_pc_for_6dof(pc: torch.Tensor, pc_mean: torch.Tensor) -> torch.Tensor:
        """
        Normalize point cloud for 6DOF-GraspNet processing.

        Args:
            pc: [N, 3] point cloud
            pc_mean: [3] original centroid

        Returns:
            torch.Tensor: [N, 3] normalized point cloud
        """
        # Center the point cloud (6DOF-GraspNet expects centered point clouds)
        pc_centered = pc - pc_mean
        return pc_centered

    @staticmethod
    def denormalize_grasps_from_6dof(grasps: torch.Tensor, pc_mean: torch.Tensor) -> torch.Tensor:
        """
        Denormalize grasps from 6DOF-GraspNet back to original coordinate system.

        Args:
            grasps: [N, 7] or [N, 4, 4] grasp poses
            pc_mean: [3] original point cloud centroid

        Returns:
            torch.Tensor: Denormalized grasps in original coordinate system
        """
        if grasps.dim() == 3 and grasps.shape[-1] == 4:
            # Homogeneous matrices format
            grasps_denorm = grasps.clone()
            grasps_denorm[..., :3, 3] += pc_mean
        else:
            # Quaternion+translation format
            grasps_denorm = grasps.clone()
            grasps_denorm[..., 4:7] += pc_mean

        return grasps_denorm


class SimpleGraspLDMInference:
    """
    Simplified GraspLDM inference interface for direct point cloud input.

    This class provides a streamlined interface that bypasses the complex ACRONYM
    dataset structure and accepts raw point cloud data directly from cameras or
    other sources. It maintains full compatibility with existing model weights
    while offering a more flexible API for real-time applications.

    Key Capabilities:
    - Direct point cloud input (no dataset required)
    - Camera pose transformation support
    - Automatic point cloud preprocessing (centering, normalization)
    - Point cloud size automatically configured from trained model
    - Multiple conditioning modes (unconditional, class, region)
    - Built-in visualization support
    - Same output format as original inference pipeline

    Dimension Flow:
    - Input PC: [N, 3] raw coordinates → [1024, 3] regularized
    - Preprocessing: centering + normalization → [1024, 3] standardized
    - LDM Inference: [1024, 3] → [num_grasps, 4, 4] transformation matrices
    """

    def __init__(
        self,
        exp_path: str,                          # Path to experiment directory
        device: str = "cuda:0",                 # Compute device
        use_ema_model: bool = True,             # Use EMA model weights
        use_fast_sampler: bool = True,          # Enable fast sampling (DDIM)
        num_inference_steps: Optional[int] = 100, # Number of denoising steps
        # 6DOF-GraspNet Integration Parameters
        enable_grasp_evaluation: bool = False,  # Enable 6DOF-GraspNet evaluation
        enable_grasp_refinement: bool = False,  # Enable grasp refinement
        evaluator_model_path: Optional[str] = None, # Path to 6DOF evaluator model
        refinement_method: str = "gradient",    # Refinement method: 'gradient' or 'sampling'
        refinement_steps: int = 5,              # Number of refinement iterations
        refinement_threshold: float = 0.7,      # Success probability threshold
    ):
        """
        Initialize the enhanced inference interface with 6DOF-GraspNet integration.

        Args:
            exp_path: Path to experiment directory containing model checkpoints
            device: PyTorch device for computation
            use_ema_model: Whether to use Exponential Moving Average weights
            use_fast_sampler: Enable fast sampling with DDIM scheduler
            num_inference_steps: Number of denoising steps for diffusion
            enable_grasp_evaluation: Enable 6DOF-GraspNet grasp evaluation
            enable_grasp_refinement: Enable grasp refinement capabilities
            evaluator_model_path: Path to 6DOF-GraspNet evaluator model
            refinement_method: Refinement method ('gradient' or 'sampling')
            refinement_steps: Number of refinement iterations
            refinement_threshold: Success probability threshold for grasp selection
        """
        self.device = torch.device(device)
        self.use_ema_model = use_ema_model

        # Store 6DOF-GraspNet integration parameters
        self.enable_grasp_evaluation = enable_grasp_evaluation and SIXDOF_AVAILABLE
        self.enable_grasp_refinement = enable_grasp_refinement and SIXDOF_AVAILABLE
        self.evaluator_model_path = evaluator_model_path
        self.refinement_method = refinement_method
        self.refinement_steps = refinement_steps
        self.refinement_threshold = refinement_threshold

        # Initialize format converter
        self.format_converter = GraspFormatConverter()

        # Initialize experiment handler
        self.experiment = Experiment(
            exp_name=os.path.basename(exp_path),
            exp_out_root=os.path.dirname(exp_path),
            modes=["ddm", "vae"]
        )

        # Load configuration
        self.config = self.experiment.get_config("ddm")
        
        # Get num_points from configuration to ensure consistency with trained model
        self.num_points = self.config.pc_num_points
        print(f"✓ Using point cloud size from config: {self.num_points}")

        # Setup diffusion sampler
        self._setup_ldm_sampler(num_inference_steps, use_fast_sampler)

        # Load model
        self.model = self._load_model()

        # Set normalization parameters (from dataset statistics)
        self._set_normalization_params()

        # Sigmoid for confidence scores
        self._sigmoid = nn.Sigmoid()

        # Initialize 6DOF-GraspNet components if enabled
        self.grasp_evaluator = None
        if self.enable_grasp_evaluation or self.enable_grasp_refinement:
            self._setup_6dof_components()

        print(f"✓ Enhanced SimpleGraspLDMInference initialized")
        print(f"  Device: {self.device}")
        print(f"  Model: {'EMA' if use_ema_model else 'Standard'}")
        print(f"  Sampler: {'Fast DDIM' if use_fast_sampler else 'Standard DDPM'}")
        print(f"  Inference steps: {self.num_inference_steps}")
        print(f"  Point cloud size: {self.num_points}")
        print(f"  6DOF Evaluation: {'✓ Enabled' if self.enable_grasp_evaluation else '✗ Disabled'}")
        print(f"  Grasp Refinement: {'✓ Enabled' if self.enable_grasp_refinement else '✗ Disabled'}")
        if self.enable_grasp_refinement:
            print(f"    Method: {self.refinement_method}")
            print(f"    Steps: {self.refinement_steps}")
            print(f"    Threshold: {self.refinement_threshold}")

    def _setup_6dof_components(self):
        """Initialize 6DOF-GraspNet evaluator and related components."""
        if not SIXDOF_AVAILABLE:
            print("❌ 6DOF-GraspNet not available, skipping component setup")
            return

        try:
            # Create options object for the evaluator following 6DOF-GraspNet pattern
            class EvaluatorOptions:
                def __init__(self, evaluator_model_path):
                    self.arch = "evaluator"
                    self.model_scale = 1
                    self.pointnet_radius = 0.02
                    self.pointnet_nclusters = 128
                    self.gpu_ids = [0] if torch.cuda.is_available() else []
                    self.init_type = 'normal'
                    self.init_gain = 0.02
                    self.is_train = False  # Important: set to False for inference
                    self.continue_train = False

                    # Set checkpoint directory and model name for automatic loading
                    if evaluator_model_path and os.path.exists(evaluator_model_path):
                        # Extract directory and epoch from path
                        self.checkpoints_dir = os.path.dirname(evaluator_model_path)
                        filename = os.path.basename(evaluator_model_path)
                        # Extract epoch from filename (e.g., "latest_net.pth" -> "latest")
                        self.which_epoch = filename.split('_net.pth')[0] if '_net.pth' in filename else 'latest'
                        self.name = os.path.basename(self.checkpoints_dir)
                    else:
                        # Use default pretrained model path
                        self.checkpoints_dir = os.path.join(os.path.dirname(__file__), '../6dof_evaluator/checkpoints')
                        self.name = 'evaluator_pretrained'
                        self.which_epoch = 'latest'

            evaluator_opt = EvaluatorOptions(self.evaluator_model_path)

            # Create the evaluator model - this will automatically load weights if available
            self.grasp_evaluator = create_6dof_model(evaluator_opt)

            print(f"  ✓ 6DOF-GraspNet evaluator initialized")
            if self.evaluator_model_path:
                print(f"    Model path: {self.evaluator_model_path}")
            else:
                print(f"    Using default pretrained model")

        except Exception as e:
            print(f"  ❌ Failed to initialize 6DOF components: {e}")
            import traceback
            traceback.print_exc()
            self.enable_grasp_evaluation = False
            self.enable_grasp_refinement = False
            self.grasp_evaluator = None

    def _setup_ldm_sampler(self, num_inference_steps: Optional[int], use_fast_sampler: bool):
        """Configure the diffusion sampler parameters."""
        if use_fast_sampler:
            self.config.models.ddm.model.args.noise_scheduler_type = "ddim"
            self.fast_sampler = "DDIM"
            self.num_inference_steps = 100 if num_inference_steps is None else num_inference_steps
        else:
            self.fast_sampler = None
            self.num_inference_steps = 1000 if num_inference_steps is None else num_inference_steps

    def _load_model(self):
        """Load the trained LDM model with VAE."""
        # Build model from configuration
        model = build_model_from_cfg(self.config.model.ddm)
        model.set_vae_model(build_model_from_cfg(self.config.model.vae))

        # Load checkpoint
        ckpt_path = self.experiment.get_ckpt_path("ddm")
        state_dict = torch.load(ckpt_path, map_location=self.device)["state_dict"]
        
        # Use appropriate model prefix (EMA vs standard)
        model_prefix = "model" if not self.use_ema_model else "ema_model.online_model"
        state_dict = fix_state_dict_prefix(state_dict, model_prefix, ignore_all_others=True)

        # Load weights
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=True)
        if missing_keys:
            warnings.warn(f"Missing keys while loading state dict: {missing_keys}")
        if unexpected_keys:
            warnings.warn(f"Found unexpected keys while loading state dict: {unexpected_keys}")

        return model.eval().to(self.device)

    def _set_normalization_params(self):
        """
        Set normalization parameters based on dataset statistics.

        These parameters are derived from the training data and must match
        exactly for proper model performance. The values are based on the
        AcronymPartialPointclouds dataset preprocessing pipeline.
        """
        # Point cloud normalization (zero-centered after per-object centering)
        self._INPUT_PC_SHIFT = torch.zeros((3,), device=self.device)
        self._INPUT_PC_SCALE = torch.ones((3,), device=self.device) * 0.05  # translation_scale

        # Grasp normalization (zero-centered after per-object centering)
        self._INPUT_GRASP_SHIFT = torch.zeros((6,), device=self.device)
        self._INPUT_GRASP_SCALE = torch.cat([
            torch.ones((3,), device=self.device) * 0.05,  # translation_scale
            torch.ones((3,), device=self.device) * 0.5,   # rotation_scale
        ])

        print(f"✓ Normalization parameters set:")
        print(f"  PC scale: {self._INPUT_PC_SCALE[0].item():.3f}")
        print(f"  Grasp translation scale: {self._INPUT_GRASP_SCALE[0].item():.3f}")
        print(f"  Grasp rotation scale: {self._INPUT_GRASP_SCALE[3].item():.3f}")

    def _prepare_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Prepare raw point cloud for inference.

        This method performs the same preprocessing steps as the original
        AcronymPartialPointclouds dataset:
        1. Convert to torch tensor
        2. Apply camera pose transformation (if provided)
        3. Regularize point count to target size
        4. Convert to float32

        Args:
            pointcloud: Raw point cloud [N, 3] in camera or world coordinates
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation

        Returns:
            torch.Tensor: Prepared point cloud [num_points, 3]
        """
        # Convert to torch tensor
        if isinstance(pointcloud, np.ndarray):
            pc = torch.from_numpy(pointcloud).float()
        else:
            pc = pointcloud.float()

        # Ensure correct shape
        if pc.ndim != 2 or pc.shape[1] != 3:
            raise ValueError(f"Point cloud must have shape [N, 3], got {pc.shape}")

        # Apply camera pose transformation if provided
        if camera_pose is not None:
            pc = self._transform_pointcloud(pc, camera_pose)

        # Regularize point count
        pc = self._regularize_pointcloud(pc, self.num_points)

        return pc.to(self.device)

    def _transform_pointcloud(
        self,
        pointcloud: torch.Tensor,
        camera_pose: Union[np.ndarray, torch.Tensor]
    ) -> torch.Tensor:
        """
        Transform point cloud using camera extrinsic parameters.

        Args:
            pointcloud: Point cloud in camera coordinates [N, 3]
            camera_pose: Camera extrinsic matrix [4, 4] (camera to world transform)

        Returns:
            torch.Tensor: Transformed point cloud [N, 3]
        """
        if isinstance(camera_pose, np.ndarray):
            camera_pose = torch.from_numpy(camera_pose).float()

        # Ensure correct shape
        if camera_pose.shape != (4, 4):
            raise ValueError(f"Camera pose must be [4, 4] matrix, got {camera_pose.shape}")

        # Convert to homogeneous coordinates
        ones = torch.ones(pointcloud.shape[0], 1, dtype=pointcloud.dtype, device=pointcloud.device)
        pc_homogeneous = torch.cat([pointcloud, ones], dim=1)  # [N, 4]

        # Apply transformation
        pc_transformed = torch.matmul(pc_homogeneous, camera_pose.T)  # [N, 4]

        # Return to 3D coordinates
        return pc_transformed[:, :3]

    def _regularize_pointcloud(self, pointcloud: torch.Tensor, target_points: int) -> torch.Tensor:
        """
        Regularize point cloud to have exactly target_points points.

        This matches the behavior of PointCloudHelpers.regularize_pc_point_count
        used in the original dataset pipeline.

        Args:
            pointcloud: Input point cloud [N, 3]
            target_points: Target number of points

        Returns:
            torch.Tensor: Regularized point cloud [target_points, 3]
        """
        current_points = pointcloud.shape[0]

        if current_points < target_points:
            # Upsample: repeat existing points
            multiplier = max(target_points // current_points, 1)
            pc = pointcloud.repeat(multiplier, 1)

            # Add random points to reach exact target
            num_extra = target_points - pc.shape[0]
            if num_extra > 0:
                extra_indices = torch.randperm(pc.shape[0])[:num_extra]
                extra_points = pc[extra_indices]
                pc = torch.cat([pc, extra_points], dim=0)

        elif current_points > target_points:
            # Downsample: random selection
            indices = torch.randperm(current_points)[:target_points]
            pc = pointcloud[indices]
        else:
            pc = pointcloud

        return pc

    def _preprocess_pointcloud(self, pointcloud: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Apply the same preprocessing pipeline as AcronymPartialPointclouds.

        This includes:
        1. Centering on point cloud mean
        2. Normalization using dataset statistics
        3. Metadata preparation for unnormalization

        Args:
            pointcloud: Prepared point cloud [num_points, 3]

        Returns:
            tuple: (preprocessed_pc, metadata_dict)
        """
        # Step 1: Center on point cloud mean
        pc_mean = torch.mean(pointcloud, dim=0)  # [3]
        pc_centered = pointcloud - pc_mean

        # Step 2: Normalize using dataset statistics
        pc_normalized = (pc_centered - self._INPUT_PC_SHIFT) / self._INPUT_PC_SCALE

        # Step 3: Prepare metadata for unnormalization
        grasp_mean = self._INPUT_GRASP_SHIFT.clone()
        grasp_mean[:3] += pc_mean  # Add centering offset to translation components

        metas = {
            "pc_mean": self._INPUT_PC_SHIFT + pc_mean,
            "pc_std": self._INPUT_PC_SCALE,
            "grasp_mean": grasp_mean,
            "grasp_std": self._INPUT_GRASP_SCALE,
            "dataset_normalized": True,
        }

        return pc_normalized, metas

    def _generate_grasps(
        self,
        pointcloud: torch.Tensor,
        metas: Dict[str, torch.Tensor],
        num_grasps: int = 20,
        return_intermediate: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Generate grasps using the LDM model.

        This method implements the core LDM inference pipeline:
        1. Point cloud encoding to latent space
        2. Diffusion sampling in grasp latent space
        3. VAE decoding to grasp poses
        4. Post-processing and unnormalization

        Args:
            pointcloud: Preprocessed point cloud [num_points, 3]
            metas: Metadata dictionary for unnormalization
            num_grasps: Number of grasps to generate
            return_intermediate: Whether to return intermediate diffusion steps

        Returns:
            dict: Generated results containing:
                - grasps: [num_grasps, 4, 4] homogeneous transformation matrices
                - confidence: [num_grasps, 1] success probabilities
                - pc: [num_points, 3] unnormalized point cloud
                - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
        """
        # Ensure batch dimension
        batch_pc = pointcloud.unsqueeze(0)  # [1, num_points, 3]

        # Move metadata to device
        metas = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                for k, v in metas.items()}

        # Prepare model input
        in_kwargs = {"xyz": batch_pc, "metas": metas}

        # Configure fast sampling if enabled
        if self.fast_sampler == "DDIM":
            self.model.set_inference_timesteps(self.num_inference_steps)

        # Execute LDM inference with intermediate step control
        with torch.no_grad():
            final_grasps, all_diffusion_grasps = self.model.generate_grasps(
                num_grasps=num_grasps, 
                return_intermediate=return_intermediate, 
                **in_kwargs
            )

        # Parse model outputs
        if self.model.vae_model.decoder._use_qualities:
            tmrp, cls_logit, qualities = final_grasps
        else:
            tmrp, cls_logit = final_grasps
            qualities = None

        # Reshape to proper dimensions
        tmrp = tmrp.view(1, num_grasps, tmrp.shape[-1])

        # Unnormalize grasp poses
        grasp_unnorm = unnormalize_grasps(tmrp, metas)

        # Convert to homogeneous transformation matrices
        H_grasps = tmrp_to_H(grasp_unnorm)  # [1, num_grasps, 4, 4]

        # Process intermediate diffusion steps if available
        all_steps_grasps = []
        if return_intermediate and all_diffusion_grasps:
            # Note: For batched inference (batch_size > 1), intermediate steps are not supported
            # This follows the same limitation as InferenceLDM
            if batch_pc.shape[0] > 1:
                print("⚠️  Warning: Batched inference with intermediate steps not supported. Skipping intermediate results.")
            else:
                for step_grasp in all_diffusion_grasps:
                    # Each step_grasp is (tmrp, cls_logit) or (tmrp, cls_logit, qualities)
                    step_tmrp = step_grasp[0]
                    # Reshape to ensure num_grasps dimension is consistently present
                    reshaped_step_tmrp = step_tmrp.view(1, num_grasps, -1)
                    step_grasp_unnorm = unnormalize_grasps(reshaped_step_tmrp, metas)
                    step_H_grasps = tmrp_to_H(step_grasp_unnorm)
                    all_steps_grasps.append(step_H_grasps.squeeze(0))  # Remove batch dimension

        # Compute confidence scores
        confidence = cls_logit.view(1, num_grasps, cls_logit.shape[-1])
        
        # Debug: Check classification head weights for anomalies
        if hasattr(self.model.vae_model.decoder, 'class_logits'):
            cls_layer = self.model.vae_model.decoder.class_logits
            weight = cls_layer.weight.data  # [1, in_features]
            bias = cls_layer.bias.data      # [1]
            print(f"🔍 Classification head weights:")
            print(f"  Weight shape: {weight.shape}")
            print(f"  Weight min/max: {weight.min().item():.6f} / {weight.max().item():.6f}")
            print(f"  Weight mean/std: {weight.mean().item():.6f} / {weight.std().item():.6f}")
            print(f"  Bias value: {bias.item():.6f}")
            print(f"  Weight norm: {weight.norm().item():.6f}")
            print(f" Weight: {weight}")
            print(f" Bias: {bias}")
            confidence -= bias.item()
        else:
            print("⚠️  Could not find classification head layer")


        # Debug: Print cls_logit statistics to diagnose confidence issues
        cls_logit_flat = cls_logit.view(-1)
        print(f"🔍 Debugging cls_logit values:")
        print(f"  Shape: {cls_logit.shape}")
        print(f"  Min: {cls_logit_flat.min().item():.6f}")
        print(f"  Max: {cls_logit_flat.max().item():.6f}")
        print(f"  Mean: {cls_logit_flat.mean().item():.6f}")
        print(f"  Std: {cls_logit_flat.std().item():.6f}")
        print(f"  First 5 values: {cls_logit_flat[:5].tolist()}")
        
        confidence = self._sigmoid(confidence)
        
        # Debug: Print confidence after sigmoid
        confidence_flat = confidence.view(-1)
        print(f"  After sigmoid - Min: {confidence_flat.min().item():.6f}")
        print(f"  After sigmoid - Max: {confidence_flat.max().item():.6f}")
        print(f"  After sigmoid - Mean: {confidence_flat.mean().item():.6f}")
        print(f"  First 5 confidence values: {confidence_flat[:5].tolist()}")
        
        # Unnormalize point cloud
        pc_unnorm = unnormalize_pc(batch_pc, metas)

        return {
            "grasps": H_grasps.squeeze(0),      # [num_grasps, 4, 4]
            "grasp_tmrp": grasp_unnorm.squeeze(0),  # [num_grasps, 6] translation + rotation MRP
            "confidence": confidence.squeeze(0), # [num_grasps, 1]
            "pc": pc_unnorm.squeeze(0),         # [num_points, 3]
            "qualities": qualities,              # Optional quality metrics
            "all_steps_grasps": all_steps_grasps, # List of intermediate diffusion steps
        }

    def infer_from_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None,
        num_grasps: int = 20,
        return_intermediate: bool = False,
        visualize: bool = False,
        return_scene: bool = False,
        save_path: str = None,
        # 6DOF-GraspNet Integration Parameters
        evaluate_with_6dof: bool = None,        # Override evaluation setting
        refine_grasps: bool = None,             # Override refinement setting
        refinement_method: str = None,          # Override refinement method
        refinement_steps: int = None            # Override refinement steps
    ) -> Union[Dict[str, torch.Tensor], Any]:
        """
        Enhanced inference method with 6DOF-GraspNet evaluation and refinement.

        This is the primary interface for the enhanced inference pipeline.
        It accepts raw point cloud data and optional camera parameters, then
        performs the complete preprocessing, inference, evaluation, and refinement pipeline.

        Args:
            pointcloud: Raw point cloud [N, 3] as numpy array or torch tensor
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation
            num_grasps: Number of grasps to generate (default: 20)
            return_intermediate: Whether to return intermediate diffusion steps (default: False)
            visualize: Whether to display 3D visualization (default: False)
            return_scene: If True and visualize=True, return scene object instead of showing
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)
            evaluate_with_6dof: Override global evaluation setting for this call
            refine_grasps: Override global refinement setting for this call
            refinement_method: Override refinement method ('gradient' or 'sampling')
            refinement_steps: Override number of refinement steps

        Returns:
            dict or Scene: If visualize=False, returns dict with:
                          - grasps: [num_grasps, 4, 4] transformation matrices
                          - grasp_tmrp: [num_grasps, 6] translation + rotation MRP
                          - confidence: [num_grasps, 1] success probabilities from graspLDM
                          - pc: [num_points, 3] processed point cloud
                          - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
                          - sixdof_scores: [num_grasps] 6DOF-GraspNet evaluation scores (if enabled)
                          - refined_grasps: [num_grasps, 4, 4] refined grasp poses (if refinement enabled)
                          - refinement_history: Refinement process data (if refinement enabled)
                          If visualize=True and return_scene=True, returns 3D scene object
                          If visualize=True and return_scene=False, shows visualization and returns dict

        Example:
            >>> # Basic usage
            >>> results = inference.infer_from_pointcloud(pc_array, num_grasps=10)
            >>> grasps = results["grasps"]  # [10, 4, 4] transformation matrices
            >>> confidence = results["confidence"]  # [10, 1] success probabilities

            >>> # With intermediate steps for analysis
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_array,
            ...     num_grasps=20,
            ...     return_intermediate=True
            ... )
            >>> intermediate_steps = results["all_steps_grasps"]  # List of intermediate results

            >>> # With camera pose transformation and saving
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_camera_coords,
            ...     camera_pose=camera_to_world_matrix,
            ...     num_grasps=20,
            ...     visualize=True,
            ...     save_path="grasps.glb"  # or .ply, .obj, .stl
            ... )
        """
        # Determine 6DOF integration settings (allow per-call overrides)
        use_6dof_eval = evaluate_with_6dof if evaluate_with_6dof is not None else self.enable_grasp_evaluation
        use_refinement = refine_grasps if refine_grasps is not None else self.enable_grasp_refinement
        refine_method = refinement_method if refinement_method is not None else self.refinement_method
        refine_steps = refinement_steps if refinement_steps is not None else self.refinement_steps

        print(f"🚀 Starting enhanced grasp inference...")
        print(f"  Input PC shape: {pointcloud.shape}")
        print(f"  Target grasps: {num_grasps}")
        print(f"  Camera pose: {'Yes' if camera_pose is not None else 'No'}")
        print(f"  Return intermediate: {'Yes' if return_intermediate else 'No'}")
        print(f"  6DOF Evaluation: {'Yes' if use_6dof_eval else 'No'}")
        print(f"  Grasp Refinement: {'Yes' if use_refinement else 'No'}")
        if use_refinement:
            print(f"    Method: {refine_method}, Steps: {refine_steps}")

        # Step 1: Prepare point cloud
        pc_prepared = self._prepare_pointcloud(pointcloud, camera_pose)
        print(f"  ✓ Point cloud prepared: {pc_prepared.shape}")

        # Step 2: Preprocess (center + normalize)
        pc_preprocessed, metas = self._preprocess_pointcloud(pc_prepared)
        pc_mean = metas['pc_mean']  # Store for 6DOF processing
        print(f"  ✓ Preprocessing complete")

        # Step 3: Generate grasps with intermediate step support
        results = self._generate_grasps(pc_preprocessed, metas, num_grasps, return_intermediate)
        print(f"  ✓ Generated {results['grasps'].shape[0]} grasps")
        if return_intermediate and results.get('all_steps_grasps'):
            print(f"  ✓ Captured {len(results['all_steps_grasps'])} intermediate steps")

        # Step 4: 6DOF-GraspNet Evaluation (if enabled)
        if use_6dof_eval:
            print(f"  🔍 Evaluating grasps with 6DOF-GraspNet...")
            sixdof_scores = self._evaluate_grasps_with_6dof(
                pc_preprocessed, results['grasps'], pc_mean
            )
            results['sixdof_scores'] = sixdof_scores
            print(f"  ✓ 6DOF evaluation complete (mean score: {sixdof_scores.mean():.3f})")

        # Step 5: Grasp Refinement (if enabled)
        if use_refinement:
            print(f"  🔧 Refining grasps with {refine_method} method...")
            refined_grasps, refined_scores = self._refine_grasps_with_6dof(
                pc_preprocessed, results['grasps'], pc_mean, refine_method, refine_steps
            )
            results['refined_grasps'] = refined_grasps
            results['refined_scores'] = refined_scores

            # Update main grasps with refined versions
            results['grasps'] = refined_grasps
            if 'sixdof_scores' not in results:
                results['sixdof_scores'] = refined_scores

            print(f"  ✓ Refinement complete (mean refined score: {refined_scores.mean():.3f})")

        # Step 4: Visualization (if requested)
        if visualize:
            scene = self._visualize_results(results, return_scene, save_path)
            if return_scene:
                return scene
            # If not returning scene, show it and continue to return results

        print(f"✅ Inference complete!")
        return results

    def _evaluate_grasps_with_6dof(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor,
        pc_mean: torch.Tensor
    ) -> torch.Tensor:
        """
        Evaluate grasps using 6DOF-GraspNet evaluator.

        Args:
            pointcloud: [num_points, 3] preprocessed point cloud
            grasps: [num_grasps, 4, 4] homogeneous transformation matrices
            pc_mean: [3] original point cloud centroid for coordinate conversion

        Returns:
            torch.Tensor: [num_grasps] success probabilities from 6DOF evaluator
        """
        if not self.enable_grasp_evaluation or self.grasp_evaluator is None:
            print("⚠️  6DOF evaluation not available, returning dummy scores")
            return torch.ones(grasps.shape[0], device=self.device) * 0.5

        try:
            # Convert grasps to 6DOF format (quaternion + translation)
            qt_grasps = self.format_converter.homogeneous_to_6dof_format(grasps)

            # Prepare point cloud for 6DOF evaluator (denormalized and centered)
            pc_for_6dof = self.format_converter.normalize_pc_for_6dof(
                pointcloud + pc_mean, pc_mean
            )

            # Generate gripper control points for each grasp using the correct function
            with torch.no_grad():
                # Convert quaternion+translation to euler angles and translations
                # as required by control_points_from_rot_and_trans
                grasp_eulers, grasp_translations = grasp_utils.convert_qt_to_rt(qt_grasps)

                # Generate control points using the correct 6DOF-GraspNet function
                grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                    grasp_eulers, grasp_translations, device=self.device
                )

                # Evaluate grasps using 6DOF evaluator
                # Note: The evaluator expects [batch_size, num_points, 3] for pc and [batch_size, num_grasps, num_control_points, 3] for grasp_pcs
                success_probs = self.grasp_evaluator.evaluate_grasps(
                    pc_for_6dof.unsqueeze(0), grasp_pcs.unsqueeze(0)
                )

                return success_probs.squeeze()

        except Exception as e:
            print(f"❌ Error in 6DOF evaluation: {e}")
            import traceback
            traceback.print_exc()
            return torch.ones(grasps.shape[0], device=self.device) * 0.5

    def _refine_grasps_with_6dof(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor,
        pc_mean: torch.Tensor,
        method: str = "gradient",
        num_steps: int = 5
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Refine grasps using 6DOF-GraspNet refinement methods.

        Args:
            pointcloud: [num_points, 3] preprocessed point cloud
            grasps: [num_grasps, 4, 4] initial grasp poses
            pc_mean: [3] original point cloud centroid
            method: Refinement method ('gradient' or 'sampling')
            num_steps: Number of refinement iterations

        Returns:
            tuple: (refined_grasps, success_probabilities)
                - refined_grasps: [num_grasps, 4, 4] refined grasp poses
                - success_probabilities: [num_grasps] final success probabilities
        """
        if not self.enable_grasp_refinement or self.grasp_evaluator is None:
            print("⚠️  6DOF refinement not available, returning original grasps")
            success_probs = self._evaluate_grasps_with_6dof(pointcloud, grasps, pc_mean)
            return grasps, success_probs

        try:
            # Convert grasps to 6DOF format
            qt_grasps = self.format_converter.homogeneous_to_6dof_format(grasps)

            # Prepare point cloud for 6DOF processing
            pc_for_6dof = self.format_converter.normalize_pc_for_6dof(
                pointcloud + pc_mean, pc_mean
            )

            # Convert to euler angles and translations for refinement
            grasp_eulers, grasp_translations = grasp_utils.convert_qt_to_rt(qt_grasps)

            # Apply refinement based on method
            if method == "gradient":
                refined_eulers, refined_translations, success_history = self._refine_gradient_based(
                    pc_for_6dof, grasp_eulers, grasp_translations, num_steps
                )
            else:  # sampling
                refined_eulers, refined_translations, success_history = self._refine_sampling_based(
                    pc_for_6dof, grasp_eulers, grasp_translations, num_steps
                )

            # Convert back to homogeneous matrices
            refined_grasps = []
            final_success = success_history[-1]

            # Get the final refined euler angles and translations
            final_eulers = torch.tensor(refined_eulers[-1], device=self.device, dtype=torch.float32)
            final_translations = torch.tensor(refined_translations[-1], device=self.device, dtype=torch.float32)

            for i in range(len(final_eulers)):
                H = torch.eye(4, device=self.device)
                # Convert euler to rotation matrix using the correct function
                R = grasp_utils.tc_rotation_matrix(
                    final_eulers[i, 0], final_eulers[i, 1], final_eulers[i, 2], batched=False
                )
                H[:3, :3] = R
                H[:3, 3] = final_translations[i]
                refined_grasps.append(H)

            refined_grasps = torch.stack(refined_grasps)

            # Denormalize back to original coordinate system
            refined_grasps = self.format_converter.denormalize_grasps_from_6dof(
                refined_grasps, pc_mean
            )

            return refined_grasps, final_success

        except Exception as e:
            print(f"❌ Error in 6DOF refinement: {e}")
            success_probs = self._evaluate_grasps_with_6dof(pointcloud, grasps, pc_mean)
            return grasps, success_probs

    def _refine_gradient_based(
        self,
        pc: torch.Tensor,
        grasp_eulers: torch.Tensor,
        grasp_translations: torch.Tensor,
        num_steps: int
    ) -> Tuple[list, list, list]:
        """
        Gradient-based grasp refinement adapted from 6DOF-GraspNet.

        Args:
            pc: Point cloud for evaluation
            grasp_eulers: Initial euler angles
            grasp_translations: Initial translations
            num_steps: Number of refinement steps

        Returns:
            tuple: (euler_history, translation_history, success_history)
        """
        # Enable gradients for optimization
        grasp_eulers = torch.autograd.Variable(grasp_eulers.to(self.device), requires_grad=True)
        grasp_translations = torch.autograd.Variable(grasp_translations.to(self.device), requires_grad=True)

        euler_history = [grasp_eulers.detach().cpu().numpy()]
        translation_history = [grasp_translations.detach().cpu().numpy()]
        success_history = []

        for step_idx in range(num_steps):
            # Generate control points from current poses
            grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                grasp_eulers, grasp_translations, self.device
            )

            # Evaluate current poses
            success = self.grasp_evaluator.evaluate_grasps(
                pc.unsqueeze(0), grasp_pcs.unsqueeze(0)
            )
            success_history.append(success.squeeze().detach().cpu().numpy())

            # Compute gradients - need to sum for scalar backward
            loss = -success.squeeze().sum()  # Negative because we want to maximize success
            loss.backward()

            # Update translations with adaptive step size
            if grasp_translations.grad is not None:
                delta_t = grasp_translations.grad
                norm_t = torch.norm(delta_t, p=2, dim=-1)
                # Avoid division by zero
                norm_t = torch.clamp(norm_t, min=1e-8)
                alpha = torch.min(0.01 / norm_t, torch.tensor(1.0).to(self.device))
                grasp_translations.data += grasp_translations.grad * alpha[:, None]

            # Update euler angles with same adaptive step size
            if grasp_eulers.grad is not None:
                grasp_eulers.data += grasp_eulers.grad * alpha[:, None]

            # Store history
            euler_history.append(grasp_eulers.detach().cpu().numpy())
            translation_history.append(grasp_translations.detach().cpu().numpy())

            # Clear gradients for next iteration
            if grasp_eulers.grad is not None:
                grasp_eulers.grad.zero_()
            if grasp_translations.grad is not None:
                grasp_translations.grad.zero_()

        return euler_history, translation_history, success_history

    def _refine_sampling_based(
        self,
        pc: torch.Tensor,
        grasp_eulers: torch.Tensor,
        grasp_translations: torch.Tensor,
        num_steps: int
    ) -> Tuple[list, list, list]:
        """
        Sampling-based grasp refinement adapted from 6DOF-GraspNet.

        Args:
            pc: Point cloud for evaluation
            grasp_eulers: Initial euler angles
            grasp_translations: Initial translations
            num_steps: Number of refinement steps

        Returns:
            tuple: (euler_history, translation_history, success_history)
        """
        euler_history = [grasp_eulers.cpu().numpy()]
        translation_history = [grasp_translations.cpu().numpy()]
        success_history = []

        # Initial evaluation
        grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
            grasp_eulers, grasp_translations, self.device
        )
        last_success = self.grasp_evaluator.evaluate_grasps(
            pc.unsqueeze(0), grasp_pcs.unsqueeze(0)
        ).squeeze()

        for step_idx in range(num_steps):
            with torch.no_grad():
                # Generate random perturbations
                delta_t = 2 * (torch.rand(grasp_translations.shape).to(self.device) - 0.5) * 0.02
                delta_euler = (torch.rand(grasp_eulers.shape).to(self.device) - 0.5) * 0.2  # Smaller perturbations for stability

                # Apply perturbations
                perturbed_translations = grasp_translations + delta_t
                perturbed_eulers = grasp_eulers + delta_euler

                # Evaluate perturbed poses
                perturbed_pcs = grasp_utils.control_points_from_rot_and_trans(
                    perturbed_eulers, perturbed_translations, self.device
                )
                perturbed_success = self.grasp_evaluator.evaluate_grasps(
                    pc.unsqueeze(0), perturbed_pcs.unsqueeze(0)
                ).squeeze()

                # Acceptance criterion (Metropolis-Hastings style)
                ratio = perturbed_success / torch.clamp(last_success, min=0.0001)
                accept_prob = torch.clamp(ratio, max=1.0)  # Cap at 1.0 for valid probabilities
                mask = torch.rand(accept_prob.shape, device=self.device) <= accept_prob

                # Update accepted samples
                ind = torch.where(mask)[0]
                if len(ind) > 0:
                    grasp_translations[ind] = perturbed_translations[ind]
                    grasp_eulers[ind] = perturbed_eulers[ind]
                    last_success[ind] = perturbed_success[ind]

                success_history.append(last_success.cpu().numpy())
                euler_history.append(grasp_eulers.cpu().numpy())
                translation_history.append(grasp_translations.cpu().numpy())

        return euler_history, translation_history, success_history

    def _visualize_results(self, results: Dict[str, torch.Tensor], return_scene: bool = False, save_path: str = None):
        """
        Visualize generated grasps in 3D with optional saving.

        Args:
            results: Results dictionary from inference
            return_scene: Whether to return scene object or show directly
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)

        Returns:
            Scene object if return_scene=True, otherwise None
        """
        # Convert to numpy for visualization
        pc_np = results["pc"].detach().cpu().numpy()
        grasps_np = results["grasps"].detach().cpu().numpy()
        confidence_np = results["confidence"].detach().cpu().numpy()

        # Create 3D scene
        scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)

        # Save scene if path provided
        if save_path is not None:
            try:
                scene.export(save_path)
                print(f"✅ Visualization saved to: {save_path}")
            except Exception as e:
                print(f"❌ Failed to save visualization: {e}")

        if return_scene:
            return scene
        else:
            scene.show(line_settings={"point_size": 10})
            return None

    def load_pointcloud_from_file(self, filepath: str) -> np.ndarray:
        """
        Load point cloud from common file formats.

        Supports .ply, .pcd, .xyz, and .txt files.

        Args:
            filepath: Path to point cloud file

        Returns:
            np.ndarray: Point cloud [N, 3]
        """
        import trimesh

        if filepath.endswith('.ply'):
            mesh = trimesh.load(filepath)
            if hasattr(mesh, 'vertices'):
                return np.array(mesh.vertices)
            else:
                raise ValueError("PLY file does not contain vertices")
        elif filepath.endswith('.pcd'):
            # Basic PCD file support
            import open3d as o3d
            pcd = o3d.io.read_point_cloud(filepath)
            return np.asarray(pcd.points)
        elif filepath.endswith(('.xyz', '.txt')):
            # Simple text format: x y z per line
            return np.loadtxt(filepath)[:, :3]
        else:
            raise ValueError(f"Unsupported file format: {filepath}")

    def filter_grasps_by_confidence(
        self,
        results: Dict[str, torch.Tensor],
        min_confidence: float = 0.5
    ) -> Dict[str, torch.Tensor]:
        """
        Filter generated grasps by confidence threshold.

        Args:
            results: Results dictionary from inference
            min_confidence: Minimum confidence threshold (0.0 to 1.0)

        Returns:
            dict: Filtered results with same structure
        """
        confidence = results["confidence"].squeeze(-1)  # [num_grasps]
        mask = confidence >= min_confidence

        if mask.sum() == 0:
            print(f"⚠️  No grasps above confidence threshold {min_confidence:.2f}")
            return results

        filtered_results = {
            "grasps": results["grasps"][mask],
            "grasp_tmrp": results["grasp_tmrp"][mask],
            "confidence": results["confidence"][mask],
            "pc": results["pc"],  # Point cloud unchanged
        }

        if "qualities" in results and results["qualities"] is not None:
            filtered_results["qualities"] = results["qualities"][mask]

        print(f"✓ Filtered {mask.sum().item()}/{len(mask)} grasps above confidence {min_confidence:.2f}")
        return filtered_results

    def get_best_grasps(
        self,
        results: Dict[str, torch.Tensor],
        top_k: int = 5
    ) -> Dict[str, torch.Tensor]:
        """
        Get top-k grasps by confidence score.

        Args:
            results: Results dictionary from inference
            top_k: Number of top grasps to return

        Returns:
            dict: Top-k results with same structure
        """
        confidence = results["confidence"].squeeze(-1)  # [num_grasps]
        _, top_indices = torch.topk(confidence, min(top_k, len(confidence)))

        top_results = {
            "grasps": results["grasps"][top_indices],
            "grasp_tmrp": results["grasp_tmrp"][top_indices],
            "confidence": results["confidence"][top_indices],
            "pc": results["pc"],  # Point cloud unchanged
        }

        if "qualities" in results and results["qualities"] is not None:
            top_results["qualities"] = results["qualities"][top_indices]

        print(f"✓ Selected top-{len(top_indices)} grasps")
        return top_results

    def filter_grasps_by_6dof_score(
        self,
        results: Dict[str, torch.Tensor],
        min_score: float = 0.5
    ) -> Dict[str, torch.Tensor]:
        """
        Filter generated grasps by 6DOF-GraspNet evaluation score.

        Args:
            results: Results dictionary from inference containing 'sixdof_scores'
            min_score: Minimum 6DOF evaluation score threshold (0.0 to 1.0)

        Returns:
            dict: Filtered results with same structure
        """
        if 'sixdof_scores' not in results:
            print("⚠️  No 6DOF scores available for filtering")
            return results

        scores = results["sixdof_scores"]
        mask = scores >= min_score

        if mask.sum() == 0:
            print(f"⚠️  No grasps above 6DOF score threshold {min_score:.2f}")
            return results

        filtered_results = {
            "grasps": results["grasps"][mask],
            "grasp_tmrp": results["grasp_tmrp"][mask],
            "confidence": results["confidence"][mask],
            "pc": results["pc"],  # Point cloud unchanged
            "sixdof_scores": results["sixdof_scores"][mask],
        }

        # Copy other optional fields if present
        for key in ["qualities", "refined_grasps", "refined_scores"]:
            if key in results and results[key] is not None:
                if isinstance(results[key], torch.Tensor) and results[key].dim() > 0:
                    filtered_results[key] = results[key][mask]
                else:
                    filtered_results[key] = results[key]

        print(f"✓ Filtered {mask.sum().item()}/{len(mask)} grasps above 6DOF score {min_score:.2f}")
        return filtered_results

    def get_best_grasps_by_6dof(
        self,
        results: Dict[str, torch.Tensor],
        top_k: int = 5
    ) -> Dict[str, torch.Tensor]:
        """
        Get top-k grasps by 6DOF-GraspNet evaluation score.

        Args:
            results: Results dictionary from inference containing 'sixdof_scores'
            top_k: Number of top grasps to return

        Returns:
            dict: Top-k results with same structure
        """
        if 'sixdof_scores' not in results:
            print("⚠️  No 6DOF scores available, falling back to confidence scores")
            return self.get_best_grasps(results, top_k)

        scores = results["sixdof_scores"]
        _, top_indices = torch.topk(scores, min(top_k, len(scores)))

        top_results = {
            "grasps": results["grasps"][top_indices],
            "grasp_tmrp": results["grasp_tmrp"][top_indices],
            "confidence": results["confidence"][top_indices],
            "pc": results["pc"],  # Point cloud unchanged
            "sixdof_scores": results["sixdof_scores"][top_indices],
        }

        # Copy other optional fields if present
        for key in ["qualities", "refined_grasps", "refined_scores"]:
            if key in results and results[key] is not None:
                if isinstance(results[key], torch.Tensor) and results[key].dim() > 0:
                    top_results[key] = results[key][top_indices]
                else:
                    top_results[key] = results[key]

        print(f"✓ Selected top-{len(top_indices)} grasps by 6DOF score")
        return top_results

    def compare_grasp_scores(self, results: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """
        Compare graspLDM confidence scores with 6DOF-GraspNet evaluation scores.

        Args:
            results: Results dictionary containing both confidence and sixdof_scores

        Returns:
            dict: Comparison statistics and analysis
        """
        if 'sixdof_scores' not in results:
            print("⚠️  No 6DOF scores available for comparison")
            return {}

        confidence = results['confidence'].squeeze().cpu().numpy()
        sixdof_scores = results['sixdof_scores'].cpu().numpy()

        # Compute correlation
        correlation = np.corrcoef(confidence, sixdof_scores)[0, 1]

        # Find agreement and disagreement cases
        conf_high = confidence > 0.7
        sixdof_high = sixdof_scores > 0.7

        agreement = np.sum(conf_high == sixdof_high)
        disagreement = np.sum(conf_high != sixdof_high)

        comparison = {
            'correlation': correlation,
            'agreement_rate': agreement / len(confidence),
            'disagreement_rate': disagreement / len(confidence),
            'confidence_stats': {
                'mean': np.mean(confidence),
                'std': np.std(confidence),
                'min': np.min(confidence),
                'max': np.max(confidence)
            },
            'sixdof_stats': {
                'mean': np.mean(sixdof_scores),
                'std': np.std(sixdof_scores),
                'min': np.min(sixdof_scores),
                'max': np.max(sixdof_scores)
            }
        }

        print(f"📊 Grasp Score Comparison:")
        print(f"  Correlation: {correlation:.3f}")
        print(f"  Agreement rate: {comparison['agreement_rate']:.3f}")
        print(f"  GraspLDM confidence: {comparison['confidence_stats']['mean']:.3f} ± {comparison['confidence_stats']['std']:.3f}")
        print(f"  6DOF-GraspNet score: {comparison['sixdof_stats']['mean']:.3f} ± {comparison['sixdof_stats']['std']:.3f}")

        return comparison

    def save_visualization(self, results: Dict[str, torch.Tensor], save_path: str,
                         include_confidence_colors: bool = True) -> bool:
        """
        Save visualization of grasps to file with enhanced options.
        
        Args:
            results: Results dictionary from inference
            save_path: Path to save the visualization
            include_confidence_colors: Whether to color grasps by confidence
            
        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Convert to numpy for visualization
            pc_np = results["pc"].detach().cpu().numpy()
            grasps_np = results["grasps"].detach().cpu().numpy()
            confidence_np = results["confidence"].detach().cpu().numpy() if include_confidence_colors else None

            # Create 3D scene
            scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)
            
            # Ensure directory exists
            import os
            dir_path = os.path.dirname(save_path)
            if dir_path:  # Only create directory if path contains a directory
                os.makedirs(dir_path, exist_ok=True)
            
            # Export scene
            scene.export(save_path)
            
            # Validate file was created
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                print(f"✅ Visualization saved to: {save_path} ({file_size} bytes)")
                return True
            else:
                print(f"❌ File was not created: {save_path}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to save visualization: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_diffusion_animation(
        self,
        results: Dict[str, torch.Tensor],
        save_path: str = "diffusion_animation.gif",
        resolution: Tuple[int, int] = (800, 600),
        fps: int = 5,
        show_confidence_colors: bool = True,
        camera_distance: float = 0.8,
        show_progress: bool = True
    ) -> bool:
        """
        Create an animated visualization of the diffusion process evolution.
        
        This method generates a GIF animation showing how grasps evolve from noise
        to final poses during the diffusion denoising process. Each frame represents
        one denoising step, providing insight into the model's generation process.
        
        Args:
            results: Results dictionary from inference containing 'all_steps_grasps'
            save_path: Path to save the animation (supports .gif, .mp4)
            resolution: Output resolution as (width, height) tuple
            fps: Frames per second for the animation
            show_confidence_colors: Whether to color grasps by confidence (final step only)
            camera_distance: Camera distance from the scene center
            show_progress: Whether to display progress information
            
        Returns:
            bool: True if animation was created successfully, False otherwise
            
        Example:
            >>> # Generate grasps with intermediate steps
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_array,
            ...     num_grasps=10,
            ...     return_intermediate=True
            ... )
            >>> 
            >>> # Create animation
            >>> success = inference.create_diffusion_animation(
            ...     results=results,
            ...     save_path="diffusion_evolution.gif",
            ...     fps=3,
            ...     resolution=(1024, 768)
            ... )
        """
        # Check if intermediate steps are available
        if not results.get('all_steps_grasps') or len(results['all_steps_grasps']) == 0:
            print("❌ No intermediate steps found in results. Please run inference with return_intermediate=True")
            return False
        
        try:
            import trimesh
            from PIL import Image
            import numpy as np
            
            # Try to import imageio for better GIF/MP4 support
            try:
                import imageio
                use_imageio = True
            except ImportError:
                print("⚠️  imageio not found, using PIL for GIF creation (MP4 not supported)")
                use_imageio = False
                if save_path.endswith('.mp4'):
                    print("❌ MP4 output requires imageio. Changing to GIF format.")
                    save_path = save_path.replace('.mp4', '.gif')
            
            print(f"🎬 Creating diffusion animation...")
            print(f"  Steps to animate: {len(results['all_steps_grasps'])}")
            print(f"  Output resolution: {resolution}")
            print(f"  Frame rate: {fps} FPS")
            print(f"  Save path: {save_path}")
            
            # Prepare point cloud data
            pc_np = results["pc"].detach().cpu().numpy()
            final_confidence = results["confidence"].detach().cpu().numpy() if show_confidence_colors else None
            
            # Create frames for each diffusion step
            frames = []
            all_steps = results['all_steps_grasps']
            total_steps = len(all_steps)
            
            for step_idx, step_grasps in enumerate(all_steps):
                if show_progress:
                    print(f"  Rendering frame {step_idx + 1}/{total_steps}...", end='\r')
                
                # Convert to numpy
                step_grasps_np = step_grasps.detach().cpu().numpy()
                
                # Use confidence colors only for the final step
                confidence_colors = final_confidence if (step_idx == total_steps - 1 and show_confidence_colors) else None
                
                # Create scene for this step
                scene = visualize_pc_grasps(pc_np, step_grasps_np, confidence_colors)
                
                # Set up camera
                bounds = scene.bounds
                center = bounds.mean(axis=0)
                scale = np.max(bounds[1] - bounds[0])
                
                # Configure camera position for consistent viewpoint
                camera_transform = np.eye(4)
                camera_transform[:3, 3] = center + np.array([0, 0, scale * camera_distance])
                
                # Render frame
                try:
                    # Use offscreen rendering
                    png_data = scene.save_image(
                        resolution=resolution,
                        camera_transform=camera_transform,
                        scene_lights='default'
                    )
                    
                    # Convert to PIL Image
                    if isinstance(png_data, bytes):
                        from io import BytesIO
                        frame = Image.open(BytesIO(png_data))
                    else:
                        frame = Image.fromarray(png_data)
                    
                    frames.append(frame)
                    
                except Exception as e:
                    print(f"\n⚠️  Warning: Failed to render frame {step_idx + 1}: {e}")
                    print("    Falling back to basic rendering...")
                    
                    # Fallback: create a simple placeholder frame
                    placeholder = Image.new('RGB', resolution, color=(50, 50, 50))
                    frames.append(placeholder)
            
            if show_progress:
                print(f"  Rendered {len(frames)} frames successfully")
            
            # Save animation
            if len(frames) == 0:
                print("❌ No frames were rendered successfully")
                return False
            
            print(f"  💾 Saving animation...")
            
            # Calculate frame duration in milliseconds
            frame_duration = int(1000 / fps)
            
            if use_imageio and save_path.endswith('.mp4'):
                # Save as MP4 using imageio
                with imageio.get_writer(save_path, fps=fps, codec='libx264') as writer:
                    for frame in frames:
                        writer.append_data(np.array(frame))
            else:
                # Save as GIF using PIL or imageio
                if use_imageio:
                    imageio.mimsave(save_path, [np.array(frame) for frame in frames], 
                                  duration=frame_duration/1000.0, loop=0)
                else:
                    frames[0].save(
                        save_path,
                        save_all=True,
                        append_images=frames[1:],
                        duration=frame_duration,
                        loop=0,
                        optimize=True
                    )
            
            # Validate output file
            import os
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                print(f"✅ Animation saved successfully!")
                print(f"  File: {save_path}")
                print(f"  Size: {file_size / (1024*1024):.2f} MB")
                print(f"  Frames: {len(frames)}")
                print(f"  Duration: {len(frames) / fps:.1f} seconds")
                return True
            else:
                print(f"❌ Failed to create animation file: {save_path}")
                return False
                
        except ImportError as e:
            print(f"❌ Missing required dependency for animation: {e}")
            print("Please install required packages: pip install trimesh pillow imageio imageio-ffmpeg")
            return False
        except Exception as e:
            print(f"❌ Failed to create animation: {e}")
            import traceback
            traceback.print_exc()
            return False

    def generate_and_animate(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None,
        num_grasps: int = 10,
        animation_path: str = "diffusion_animation.gif",
        resolution: Tuple[int, int] = (800, 600),
        fps: int = 5,
        **kwargs
    ) -> Tuple[Dict[str, torch.Tensor], bool]:
        """
        Convenience method to generate grasps and create diffusion animation in one call.
        
        This method combines inference with intermediate steps and animation generation
        for a streamlined workflow when analyzing the diffusion process.
        
        Args:
            pointcloud: Raw point cloud [N, 3] as numpy array or torch tensor
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation
            num_grasps: Number of grasps to generate (default: 10)
            animation_path: Path to save the animation file (default: "diffusion_animation.gif")
            resolution: Output resolution as (width, height) tuple (default: (800, 600))
            fps: Frames per second for the animation (default: 5)
            **kwargs: Additional arguments passed to infer_from_pointcloud
            
        Returns:
            tuple: (results_dict, animation_success) where:
                - results_dict: Complete inference results with intermediate steps
                - animation_success: Boolean indicating if animation was created successfully
                
        Example:
            >>> # Generate grasps and animation in one call
            >>> results, animation_ok = inference.generate_and_animate(
            ...     pointcloud=pc_array,
            ...     num_grasps=15,
            ...     animation_path="my_diffusion.gif",
            ...     fps=4
            ... )
            >>> 
            >>> if animation_ok:
            ...     print(f"Animation saved! Results contain {len(results['all_steps_grasps'])} steps")
        """
        print(f"🚀 Starting combined inference and animation generation...")
        
        # Step 1: Generate grasps with intermediate steps
        results = self.infer_from_pointcloud(
            pointcloud=pointcloud,
            camera_pose=camera_pose,
            num_grasps=num_grasps,
            return_intermediate=True,  # Force intermediate steps for animation
            visualize=False,  # Skip visualization to avoid conflicts
            **kwargs
        )
        
        # Step 2: Create animation from intermediate steps
        if results.get('all_steps_grasps'):
            print(f"✓ Generated {len(results['all_steps_grasps'])} intermediate steps")
            animation_success = self.create_diffusion_animation(
                results=results,
                save_path=animation_path,
                resolution=resolution,
                fps=fps
            )
        else:
            print("❌ No intermediate steps available for animation")
            animation_success = False
        
        print(f"✅ Combined generation complete!")
        print(f"  Final grasps: {results['grasps'].shape[0]}")
        print(f"  Animation: {'✅ Success' if animation_success else '❌ Failed'}")
        
        return results, animation_success


def demo_simple_inference():
    """
    Demonstration of the simplified inference interface.

    This function shows how to use the SimpleGraspLDMInference class
    with synthetic data to generate grasps.
    """
    print("🎯 GraspLDM Simplified Inference Demo")
    print("=" * 50)

    # Initialize inference engine
    exp_path = "checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k"

    if not os.path.exists(exp_path):
        print(f"❌ Experiment path not found: {exp_path}")
        print("Please ensure you have downloaded the model checkpoints.")
        return

    try:
        inference = SimpleGraspLDMInference(
            exp_path=exp_path,
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            num_inference_steps=100  # Faster for demo
        )

        # Generate synthetic point cloud (sphere)
        print("\n📊 Generating synthetic point cloud...")
        theta = np.random.uniform(0, 2*np.pi, 2000)
        phi = np.random.uniform(0, np.pi, 2000)
        r = 0.18  # 18cm radius sphere

        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)

        synthetic_pc = np.column_stack([x, y, z])
        print(f"✓ Created sphere point cloud: {synthetic_pc.shape}")

        # Generate grasps
        print("\n🤖 Generating grasps...")
        results = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=False  # Set to True to see 3D visualization
        )

        # Demonstrate intermediate steps feature
        print("\n🔍 Generating grasps with intermediate steps...")
        results_with_steps = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=10,
            return_intermediate=True,
            visualize=False
        )

        # Demonstrate saving visualization
        print("\n💾 Saving visualization...")
        results_with_viz = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=True,
            save_path="demo_grasps.glb"  # Save as GLB format
        )

        # Analyze results
        print(f"\n📈 Results Analysis:")
        print(f"  Generated grasps: {results['grasps'].shape[0]}")
        print(f"  Confidence range: {results['confidence'].min():.3f} - {results['confidence'].max():.3f}")
        print(f"  Mean confidence: {results['confidence'].mean():.3f}")
        
        # Analyze intermediate steps
        if results_with_steps.get('all_steps_grasps'):
            print(f"  Intermediate steps captured: {len(results_with_steps['all_steps_grasps'])}")
            print(f"  Each step shape: {results_with_steps['all_steps_grasps'][0].shape}")
            
            # Create diffusion animation
            print("\n🎬 Creating diffusion animation...")
            animation_success = inference.create_diffusion_animation(
                results=results_with_steps,
                save_path="demo_diffusion_evolution.gif",
                resolution=(800, 600),
                fps=3,
                show_confidence_colors=True
            )
            
            if animation_success:
                print("  ✅ Animation created successfully!")
            else:
                print("  ❌ Animation creation failed")
        else:
            print(f"  No intermediate steps captured (may require return_intermediate=True)")

        # Filter high-confidence grasps
        good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.7)

        # Get top grasps
        top_grasps = inference.get_best_grasps(results, top_k=5)

        print(f"\n✅ Demo completed successfully!")
        print(f"  High-confidence grasps: {good_grasps['grasps'].shape[0]}")
        print(f"  Top-5 grasps confidence: {top_grasps['confidence'].squeeze().tolist()}")

        # Demonstrate the convenience method for combined generation and animation
        print(f"\n🎬 Demonstrating combined generation and animation...")
        try:
            combined_results, combined_success = inference.generate_and_animate(
                pointcloud=synthetic_pc,
                num_grasps=8,
                animation_path="demo_combined_diffusion.gif",
                resolution=(640, 480),
                fps=4
            )
            
            if combined_success:
                print(f"  ✅ Combined demo successful!")
                print(f"     Generated: {combined_results['grasps'].shape[0]} grasps")
                print(f"     Animation: demo_combined_diffusion.gif")
            else:
                print(f"  ⚠️  Combined demo completed with animation issues")
                
        except Exception as e:
            print(f"  ⚠️  Combined demo skipped due to: {e}")

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    demo_simple_inference()
