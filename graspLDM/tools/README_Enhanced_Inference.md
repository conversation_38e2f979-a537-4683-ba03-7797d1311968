# Enhanced SimpleGraspLDMInference with 6DOF-GraspNet Integration

This document describes the enhanced version of `SimpleGraspLDMInference` that integrates 6DOF-GraspNet's grasp evaluation and refinement capabilities with GraspLDM's diffusion-based grasp generation.

## Overview

The enhanced interface provides:

- **Original GraspLDM functionality**: Diffusion-based grasp generation from point clouds
- **6DOF-GraspNet evaluation**: Success probability scoring using a trained evaluator network
- **Grasp refinement**: Iterative optimization using gradient-based or sampling-based methods
- **Backward compatibility**: Existing code continues to work unchanged
- **Advanced utilities**: Filtering, comparison, and analysis tools

## Installation Requirements

### Basic Requirements (GraspLDM only)
```bash
# Standard GraspLDM dependencies
pip install torch torchvision numpy trimesh
```

### Enhanced Features (6DOF-GraspNet integration)
```bash
# Additional dependencies for 6DOF integration
# Ensure pytorch_6dof-graspnet is available in the parent directory
cd ../pytorch_6dof-graspnet
pip install -r requirements.txt
```

## Quick Start

### Basic Usage (Original Interface)
```python
from simple_inference import SimpleGraspLDMInference
import numpy as np

# Initialize inference engine
inference = SimpleGraspLDMInference(
    exp_path="path/to/graspldm/experiment"
)

# Generate grasps from point cloud
pc = np.random.randn(2000, 3)  # Your point cloud data
results = inference.infer_from_pointcloud(
    pointcloud=pc,
    num_grasps=20
)

print(f"Generated {len(results['grasps'])} grasps")
```

### Enhanced Usage with 6DOF Integration
```python
# Initialize with 6DOF features enabled
inference = SimpleGraspLDMInference(
    exp_path="path/to/graspldm/experiment",
    enable_grasp_evaluation=True,
    enable_grasp_refinement=True,
    evaluator_model_path="path/to/6dof_evaluator.pth",
    refinement_method="gradient",
    refinement_steps=5
)

# Generate, evaluate, and refine grasps
results = inference.infer_from_pointcloud(
    pointcloud=pc,
    num_grasps=20,
    evaluate_with_6dof=True,
    refine_grasps=True
)

print(f"GraspLDM confidence: {results['confidence'].mean():.3f}")
print(f"6DOF scores: {results['sixdof_scores'].mean():.3f}")
print(f"Refined scores: {results['refined_scores'].mean():.3f}")
```

## Configuration Parameters

### Initialization Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `exp_path` | str | Required | Path to GraspLDM experiment directory |
| `device` | str | "cuda:0" | PyTorch device for computation |
| `use_ema_model` | bool | True | Use Exponential Moving Average weights |
| `use_fast_sampler` | bool | True | Enable DDIM fast sampling |
| `num_inference_steps` | int | 100 | Number of diffusion denoising steps |
| `enable_grasp_evaluation` | bool | False | Enable 6DOF-GraspNet evaluation |
| `enable_grasp_refinement` | bool | False | Enable grasp refinement |
| `evaluator_model_path` | str | None | Path to 6DOF evaluator model |
| `refinement_method` | str | "gradient" | Refinement method: 'gradient' or 'sampling' |
| `refinement_steps` | int | 5 | Number of refinement iterations |
| `refinement_threshold` | float | 0.7 | Success probability threshold |

### Inference Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `pointcloud` | array | Required | Input point cloud [N, 3] |
| `camera_pose` | array | None | Camera extrinsic matrix [4, 4] |
| `num_grasps` | int | 20 | Number of grasps to generate |
| `return_intermediate` | bool | False | Return diffusion intermediate steps |
| `visualize` | bool | False | Show 3D visualization |
| `evaluate_with_6dof` | bool | None | Override evaluation setting |
| `refine_grasps` | bool | None | Override refinement setting |
| `refinement_method` | str | None | Override refinement method |
| `refinement_steps` | int | None | Override refinement steps |

## Output Format

### Basic Results
```python
results = {
    'grasps': torch.Tensor,          # [N, 4, 4] transformation matrices
    'grasp_tmrp': torch.Tensor,      # [N, 6] translation + rotation MRP
    'confidence': torch.Tensor,      # [N, 1] GraspLDM confidence scores
    'pc': torch.Tensor,              # [num_points, 3] processed point cloud
    'all_steps_grasps': List,        # Intermediate diffusion steps (optional)
}
```

### Enhanced Results (with 6DOF integration)
```python
results = {
    # ... basic results ...
    'sixdof_scores': torch.Tensor,   # [N] 6DOF-GraspNet evaluation scores
    'refined_grasps': torch.Tensor,  # [N, 4, 4] refined grasp poses
    'refined_scores': torch.Tensor,  # [N] success scores after refinement
}
```

## Advanced Features

### Grasp Filtering and Selection
```python
# Filter by confidence threshold
good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.7)

# Filter by 6DOF evaluation score
good_6dof = inference.filter_grasps_by_6dof_score(results, min_score=0.6)

# Get top-k grasps by different metrics
top_confidence = inference.get_best_grasps(results, top_k=5)
top_6dof = inference.get_best_grasps_by_6dof(results, top_k=5)
```

### Score Comparison and Analysis
```python
# Compare GraspLDM and 6DOF-GraspNet scoring
comparison = inference.compare_grasp_scores(results)
print(f"Correlation: {comparison['correlation']:.3f}")
print(f"Agreement rate: {comparison['agreement_rate']:.3f}")
```

### Visualization and Saving
```python
# Save 3D visualization
inference.save_visualization(results, "grasps.glb")

# Create diffusion animation (requires intermediate steps)
results = inference.infer_from_pointcloud(
    pointcloud=pc,
    num_grasps=10,
    return_intermediate=True
)
inference.create_diffusion_animation(results, "diffusion.gif")
```

## Refinement Methods

### Gradient-Based Refinement
- Uses backpropagation through the evaluator network
- Computes gradients of success probability w.r.t. grasp parameters
- Updates poses in the direction of increasing success probability
- Generally faster but requires differentiable evaluator

### Sampling-Based Refinement
- Uses random perturbations with acceptance/rejection
- Metropolis-Hastings style acceptance criterion
- More robust to local minima
- Works with any evaluator (differentiable or not)

## Examples

### Complete Workflow Example
```python
import numpy as np
from simple_inference import SimpleGraspLDMInference

# Load your point cloud
pc = np.load("object_pointcloud.npy")  # [N, 3]

# Initialize enhanced inference
inference = SimpleGraspLDMInference(
    exp_path="checkpoints/graspldm_model",
    enable_grasp_evaluation=True,
    enable_grasp_refinement=True,
    refinement_method="gradient",
    refinement_steps=10
)

# Generate and refine grasps
results = inference.infer_from_pointcloud(
    pointcloud=pc,
    num_grasps=30,
    evaluate_with_6dof=True,
    refine_grasps=True,
    visualize=True,
    save_path="final_grasps.glb"
)

# Analyze results
print(f"Generated {len(results['grasps'])} grasps")
print(f"Mean 6DOF score: {results['sixdof_scores'].mean():.3f}")
print(f"Refinement improvement: {(results['refined_scores'] - results['sixdof_scores']).mean():.3f}")

# Get best grasps for execution
best_grasps = inference.get_best_grasps_by_6dof(results, top_k=5)
print(f"Top 5 grasp scores: {best_grasps['sixdof_scores'].tolist()}")
```

## Testing

Run the test suite to verify functionality:
```bash
cd graspLDM/tools
python test_enhanced_inference.py
```

Run the demonstration script:
```bash
python enhanced_inference_demo.py
```

## Troubleshooting

### Common Issues

1. **6DOF-GraspNet not available**
   - Ensure `pytorch_6dof-graspnet` is in the parent directory
   - Install required dependencies
   - Features will gracefully disable if not available

2. **Model path not found**
   - Verify GraspLDM model checkpoints are downloaded
   - Check the experiment path is correct

3. **CUDA out of memory**
   - Reduce `num_grasps` or `num_inference_steps`
   - Use CPU device for testing
   - Reduce point cloud size

4. **Slow refinement**
   - Reduce `refinement_steps`
   - Use "sampling" method instead of "gradient"
   - Process fewer grasps at once

## Performance Notes

- **6DOF evaluation** adds ~10-20% overhead per grasp
- **Gradient refinement** is faster but requires more memory
- **Sampling refinement** is slower but more memory efficient
- **Intermediate steps** significantly increase memory usage

## Backward Compatibility

The enhanced interface is fully backward compatible. Existing code using `SimpleGraspLDMInference` will continue to work unchanged. New features are opt-in through configuration parameters.
