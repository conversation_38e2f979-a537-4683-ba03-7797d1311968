import os

# Input/Output: grasp representation [mrp(3), t(3), cls_success(1), qualities(4)]
grasp_pose_dims = 6
num_output_qualities = 0

grasp_representation_dims = (
    grasp_pose_dims + num_output_qualities + 1
    if num_output_qualities is not None
    else grasp_pose_dims + 1
)

grasp_latent_dims = 16
pc_latent_dims = 256
pc_latent_channels = 3

pc_num_points = 1024
batch_num_scenes = 10
# max_scenes = 10

# Max batch steps or epochs. Only one of them should be defined. If both, steps will considered.
max_steps = 180000
max_epochs = None

## Checkpoints:
# If to auto check the exp directory and resume from last saved checkpoints
resume_training_from_last = True

# TODO: Not passed in config.
save_ckpt_every_n_epochs = 50
# During training, if a ckpt is provided here, it overrides resume_training_from_last and instead resumes training from this ckpt
vae_ckpt_path = None  # "output/boilerplate_kldanneal_c0.1/vae/checkpoints/last.ckpt"
ddm_ckpt_path = None


## -- Model --
dropout = 0.1  # or None

pc_encoder_config = dict(
    type="PVCNNEncoder",
    args=dict(
        in_features=3,
        n_points=pc_num_points,
        scale_channels=0.75,
        scale_voxel_resolution=0.75,
        num_blocks=(1, 1, 1, 1),
        out_channels=pc_latent_channels,
        use_global_attention=False,
    ),
)

grasp_encoder_config = dict(
    type="ResNet1D",
    args=dict(
        in_features=grasp_representation_dims,
        block_channels=(32, 64, 128, 256),
        input_conditioning_dims=pc_latent_dims,
        resnet_block_groups=4,
        dropout=dropout,
    ),
)

decoder_config = dict(
    type="ResNet1D",
    args=dict(
        block_channels=(32, 64, 128, 256),
        # out_dim=grasp_pose_dims,
        input_conditioning_dims=pc_latent_dims,
        resnet_block_groups=4,
        dropout=dropout,
    ),
)

loss_config = dict(
    reconstruction_loss=dict(
        type="GraspReconstructionLoss",
        name="reconstruction_loss",
        args=dict(translation_weight=1, rotation_weight=1),
    ),
    latent_loss=dict(
        type="VAELatentLoss",
        args=dict(
            name="grasp_latent",
            cyclical_annealing=True,
            num_steps=max_steps,
            num_cycles=1,
            ratio=0.5,
            start=1e-7,
            stop=0.1,
        ),
    ),
    classification_loss=dict(type="ClassificationLoss", args=dict(weight=0.1)),
    # quality_loss=dict(type="QualityLoss", args=dict(weight=0.1)),
)

denoiser_model = dict(
    type="TimeConditionedResNet1D",
    args=dict(
        dim=grasp_latent_dims,
        channels=1,
        block_channels=(32, 64, 128, 256),
        input_conditioning_dims=pc_latent_dims,
        resnet_block_groups=4,
        dropout=dropout,
        is_time_conditioned=True,
        learned_variance=False,
        learned_sinusoidal_cond=False,
        random_fourier_features=True,
        # learned_sinusoidal_dim=16,
    ),
)
# Use `model` for single module to be built. If a list of modules are required to be built, use `models` to make sure the outer
# See models/builder.py for more info.
models = dict(
    vae=dict(
        model=dict(
            type="GraspCVAE",
            args=dict(
                grasp_latent_size=grasp_latent_dims,
                pc_latent_size=pc_latent_dims,
                pc_encoder_config=pc_encoder_config,
                grasp_encoder_config=grasp_encoder_config,
                decoder_config=decoder_config,
                loss_config=loss_config,
                num_output_qualities=num_output_qualities,
                intermediate_feature_resolution=16,
            ),
        ),
        ckpt_path=vae_ckpt_path,
    ),
    ddm=dict(
        model=dict(
            type="GraspLatentDDM",
            args=dict(
                model=denoiser_model,
                latent_in_features=grasp_latent_dims,
                diffusion_timesteps=1000,
                noise_scheduler_type="ddpm",
                diffusion_loss="l2",
                beta_schedule="linear",
                is_conditioned=True,
                joint_training=False,
                denoising_loss_weight=1,
                variance_type="fixed_large",
                elucidated_diffusion=False,
                beta_start=0.00005,
                beta_end=0.001,
            ),
        ),
        ckpt_path=ddm_ckpt_path,
    ),
)
## -- Data --
augs_config = [
    dict(type="RandomRotation", args=dict(p=0.5, max_angle=180, is_degree=True)),
    dict(type="PointcloudJitter", args=dict(p=1, sigma=0.005, clip=0.005)),
    dict(type="RandomPointcloudDropout", args=dict(p=0.5, max_dropout_ratio=0.4)),
]

root_data_dir = (
    "/mnt/irisgpfs/projects/mis-urso/grasp/data/acronym/renders/objects_filtered_grasps_63cat_8k/"
)
camera_json = "data/cameras/camera_d435i_dummy.json"
max_scenes = None
train_data = dict(
    type="AcronymPartialPointclouds",
    args=dict(
        data_root_dir=root_data_dir,
        max_scenes=max_scenes,
        camera_json=camera_json,
        num_points_per_pc=pc_num_points,
        num_grasps_per_obj=100,
        rotation_repr="mrp",
        augs_config=augs_config,
        split="train",
        depth_px_scale=10000,
        scene_prefix="scene_",
        min_usable_pc_points=1024,
        preempt_load_data=True,
        use_failed_grasps=False,
        failed_grasp_ratio=0.3,
        load_fixed_grasp_transforms=None,
        is_input_dataset_normalized=False,
    ),
)

data = dict(
    train=train_data,
)

# Patch: Mesh Categories. Used for simulation
mesh_root = "/home/<USER>/phd/data/ACRONYM/"
mesh_root = (
    mesh_root
    if os.path.exists(mesh_root)
    else "/mnt/irisgpfs/users/kbarad/grasp/data/acronym"
)
mesh_categories = ["Cup", "Mug", "Fork", "Hat", "Bottle", "Bowl", "Car", "Donut", "Laptop", "MousePad", "Pencil", "Plate", "ScrewDriver", "WineBottle", "Backpack", "Bag", "Banana", "Battery", "BeanBag", "Bear", "Book", "Books", "Camera", "CerealBox", "Cookie", "Hammer", "Hanger", "Knife", "MilkCarton", "Painting", "PillBottle", "Plant", "PowerSocket", "PowerStrip", "PS3", "PSP", "Ring", "Scissors", "Shampoo", "Shoes", "Sheep", "Shower", "Sink", "SoapBottle", "SodaCan", "Spoon", "Statue", "Teacup", "Teapot", "ToiletPaper", "ToyFigure", "Wallet", "WineGlass", "Cow", "Sheep", "Cat", "Dog", "Pizza", "Elephant", "Donkey", "RubiksCube", "Tank", "Truck", "USBStick"]

## Logger
logger = dict(type="WandbLogger", project="partial-pc-baseline")

optimizer = dict(
    initial_lr=0.001,
    scheduler=dict(
        type="MultiStepLR",
        args=dict(milestones=[int(max_steps / 3), int(2 * max_steps / 3)], gamma=0.1),
    ),
)

num_gpus = 1


steps_or_epochs = (
    dict(max_steps=max_steps) if max_steps is not None else dict(max_epochs=max_epochs)
)

train = dict(
    **steps_or_epochs,
    batch_size=batch_num_scenes,
    num_workers=7 * num_gpus,
    accelerator="gpu",
    devices=num_gpus,
    strategy="ddp",
)
