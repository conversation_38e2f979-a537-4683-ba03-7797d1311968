# 3D可视化保存功能

## 概述

为 GraspLDM 的 `simple_inference.py` 增加了3D可视化结果保存功能，支持多种文件格式。

## 新增功能

### 1. 直接保存参数
在 `infer_from_pointcloud` 方法中添加了 `save_path` 参数：

```python
results = inference.infer_from_pointcloud(
    pointcloud=pc_array,
    num_grasps=20,
    visualize=True,
    save_path="output/grasps.glb"  # 新增保存路径参数
)
```

### 2. 专用保存方法
新增 `save_visualization` 方法提供更多控制选项：

```python
# 基本保存
success = inference.save_visualization(results, "grasps.glb")

# 不包含置信度颜色的保存
success = inference.save_visualization(
    results, 
    "grasps_plain.ply", 
    include_confidence_colors=False
)
```

### 3. 支持的文件格式

| 格式 | 扩展名 | 特点 |
|------|--------|------|
| **GLB** | `.glb` | 推荐格式，紧凑的二进制GLTF，支持颜色和材质 |
| **PLY** | `.ply` | 点云和网格，支持颜色 |
| **OBJ** | `.obj` | 通用3D格式，广泛支持 |
| **STL** | `.stl` | 3D打印常用格式 |

### 4. 增强的错误处理
- 自动创建输出目录
- 文件创建验证
- 详细的错误信息
- 文件大小报告

## 使用示例

### 基本使用
```python
from tools.simple_inference import SimpleGraspLDMInference

# 初始化推理引擎
inference = SimpleGraspLDMInference(exp_path="path/to/model")

# 推理并保存
results = inference.infer_from_pointcloud(
    pointcloud=point_cloud_data,
    num_grasps=10,
    visualize=True,
    save_path="output/my_grasps.glb"
)
```

### 批量保存不同格式
```python
# 获取推理结果
results = inference.infer_from_pointcloud(point_cloud_data, num_grasps=10)

# 保存为不同格式
formats = [".glb", ".ply", ".obj", ".stl"]
for fmt in formats:
    inference.save_visualization(results, f"output/grasps{fmt}")
```

### 过滤后保存
```python
# 生成抓取
results = inference.infer_from_pointcloud(point_cloud_data, num_grasps=50)

# 过滤高置信度抓取
good_grasps = inference.filter_grasps_by_confidence(results, min_confidence=0.8)

# 保存过滤后的结果
inference.save_visualization(good_grasps, "output/high_confidence_grasps.glb")
```

## 测试功能

运行保存功能测试：
```bash
cd graspLDM
python tools/simple_inference.py --test-save
```

## 技术细节

### 文件格式特性
- **GLB**: 最推荐，包含完整的几何、颜色和材质信息
- **PLY**: 适合点云数据，支持顶点颜色
- **OBJ**: 简单的几何格式，兼容性好
- **STL**: 仅几何信息，不支持颜色

### 颜色编码
- 点云：基于XYZ坐标的RGB映射
- 抓取器：基于置信度的绿色-红色梯度
  - 高置信度：亮绿色
  - 低置信度：深红色

### 性能考虑
- 文件大小：GLB < PLY < OBJ < STL
- 加载速度：GLB和PLY较快
- 兼容性：OBJ和STL兼容性最好

## 故障排除

### 常见问题
1. **导出失败**: 检查trimesh是否正确安装
2. **目录不存在**: 函数会自动创建必要的目录
3. **文件过大**: 考虑减少抓取数量或使用GLB格式

### 调试输出
函数提供详细的状态信息：
- ✅ 保存成功：显示文件路径和大小
- ❌ 保存失败：显示具体错误信息

## 更新历史

- v1.0: 基本保存功能，支持4种格式
- v1.1: 添加专用保存方法和增强错误处理
- v1.2: 添加置信度颜色控制选项 