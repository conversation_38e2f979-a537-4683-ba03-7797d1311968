import os
import warnings

import einops
import torch
import torch.nn as nn
import torcheval.metrics.functional as Metrics
from pytorch_lightning.callbacks import (
    DeviceStatsMonitor,
    LearningRateMonitor,
    ModelCheckpoint,
    ModelSummary,
    StochasticWeightAveraging,
)
from pytorch_lightning.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from torch.utils.data import Dataset
from utils.rotations import tmrp_to_H

from grasp_ldm.dataset.builder import build_dataset_from_cfg
from grasp_ldm.models.builder import build_model_from_cfg
from grasp_ldm.utils.config import Config, ConfigDict

from .experiment import Experiment
from .trainer import LightningTrainer


class GraspClassificationTrainer(LightningTrainer):
    """
    Grasp Classification Trainer for Binary Grasp Success Prediction
    
    =============================================================================
    PIPELINE OVERVIEW
    =============================================================================
    
    This trainer implements a binary classification pipeline for predicting grasp success.
    The model takes point clouds and grasp poses as input and outputs success probabilities.
    
    ARCHITECTURE:
    - Model: PointsBasedGraspClassifier (typically PVCNN-based)
    - Input: Point cloud [B, N, 3] + Grasp poses [B, num_grasps, 6]
    - Output: Binary classification logits/probabilities [B*num_grasps, 1]
    
    =============================================================================
    DATA FLOW DURING TRAINING/VALIDATION:
    =============================================================================
    
    1. INPUT BATCH STRUCTURE:
       - pc: [B, N, 3] - Point cloud coordinates
       - grasps: [B, num_grasps, 6] - Grasp poses (translation + rotation)
       - success: [B, num_grasps] - Binary success labels
       - metas: Additional metadata
    
    2. DATA RESHAPING FOR 1-1 PAIRING:
       - pc: [B, N, 3] → [B*num_grasps, N, 3] (repeat_interleave)
       - grasps: [B, num_grasps, 6] → [B*num_grasps, 6] (rearrange)
       - success: [B, num_grasps] → [B*num_grasps] (flatten)
    
    3. MODEL FORWARD PASS:
       - Input: pc [B*num_grasps, N, 3], grasps [B*num_grasps, 6]
       - Processing: Point cloud + grasp points → PVCNN → classifier
       - Output: loss, predictions [B*num_grasps, 1]
    
    4. METRICS COMPUTATION:
       - Convert probabilities to binary predictions using threshold (0.5)
       - Compute: accuracy, precision, recall, F1-score, average precision
    
    =============================================================================
    KEY DIMENSION TRANSFORMATIONS:
    =============================================================================
    
    Original batch:
    - pc: [B, N, 3]
    - grasps: [B, num_grasps, 6] 
    - success: [B, num_grasps]
    
    After reshaping for 1-1 pairing:
    - pc: [B*num_grasps, N, 3]
    - grasps: [B*num_grasps, 6]
    - success: [B*num_grasps]
    
    Model output:
    - predictions: [B*num_grasps, 1] or [B*num_grasps] after squeeze
    
    =============================================================================
    """
    
    # Classification threshold for converting probabilities to binary predictions
    CLS_PRED_THRESHOLD = 0.5

    def __init__(self, config: Config = None):
        """
        Initialize Grasp Classification Trainer
        
        Args:
            config (Config): Configuration object containing:
                - model: Model configuration (typically PointsBasedGraspClassifier)
                - data: Dataset configuration for train/val splits
                - trainer: Training configuration (optimizer, scheduler, etc.)
        
        The trainer sets up:
        1. Experiment management and checkpointing
        2. Model building (PointsBasedGraspClassifier)
        3. Dataset building (typically AcronymGraspPointsClassification)
        4. Parent LightningTrainer initialization
        """

        # Split main sub-configs for better organization
        model_config = config.model
        data_config = config.data
        trainer_config = config.trainer

        # Experiment management and config storage
        self._config = config
        self._experiment = Experiment(config.filename)

        # Checkpointing configuration
        # Default to saving checkpoints every 1000 steps if not specified
        self._checkpointing_freq = (
            trainer_config.checkpointing_freq
            if hasattr(trainer_config, "checkpointing_freq")
            else 1000
        )
        trainer_config.default_root_dir = self._experiment.ckpt_dir

        # Initialize parent trainer class with all configurations
        super().__init__(model_config, data_config, trainer_config)

        # Set up checkpoint resuming if available
        self.resume_from_checkpoint = self._experiment.default_resume_checkpoint

    def _build_dataset(self, data_config, split):
        """
        Build dataset for the specified split
        
        Args:
            data_config: Dataset configuration
            split (str): Dataset split ('train', 'val', 'test')
            
        Returns:
            Dataset: Built dataset with grasp classification data
            
        The dataset typically returns:
        - pc: [N, 3] point cloud
        - grasps: [num_grasps, 6] grasp poses
        - success: [num_grasps] binary success labels
        - metas: additional metadata
        """
        dataset = build_dataset_from_cfg(data_config, split)

        # Pre-load any necessary data before spawning workers
        # This typically loads grasp data, point clouds, etc.
        dataset.pre_load()

        return dataset

    def _build_model(self, model_config):
        """
        Build the grasp classification model
        
        Args:
            model_config: Model configuration
            
        Returns:
            nn.Module: PointsBasedGraspClassifier model
            
        The model typically consists of:
        - PVCNN backbone for point cloud processing
        - Classifier head for binary classification
        - Built-in loss computation (BCEWithLogitsLoss)
        """
        model = build_model_from_cfg(ConfigDict(model=model_config))

        ## TODO: custom model initialization, if any
        # model.initialize()

        return model

    def training_step(self, batch_data, batch_idx):
        """
        Single training step with detailed dimension tracking
        
        Args:
            batch_data (dict): Batch containing:
                - pc: [B, N, 3] point clouds
                - grasps: [B, num_grasps, 6] grasp poses  
                - success: [B, num_grasps] binary labels
                - metas: additional metadata
            batch_idx (int): Batch index
            
        Returns:
            torch.Tensor: Computed loss for backpropagation
            
        DIMENSION FLOW:
        1. Input: pc [B, N, 3], grasps [B, num_grasps, 6], success [B, num_grasps]
        2. Reshape: pc → [B*num_grasps, N, 3], grasps → [B*num_grasps, 6], success → [B*num_grasps]
        3. Forward: → loss (scalar), predictions [B*num_grasps]
        """

        # Extract inputs from batch
        pc = batch_data["pc"]  # [B, N, 3] - Point cloud coordinates
        grasps = batch_data["grasps"]  # [B, num_grasps, 6] - Grasp poses (t+r)

        # Flatten success labels for loss computation
        # TODO: verify this reshape consistency across different datasets
        success_labels = batch_data["success"].view(-1)  # [B*num_grasps] - Binary labels
        num_grasps = grasps.shape[1]  # Number of grasps per point cloud

        # Create 1-1 pairing between point clouds and grasps
        # Each grasp needs to be paired with the corresponding point cloud
        pc = pc.repeat_interleave(num_grasps, dim=0)  # [B, N, 3] → [B*num_grasps, N, 3]
        grasps = einops.rearrange(grasps, "b n c d -> (b n) c d")  # [B, num_grasps, 6] → [B*num_grasps, 6]

        # Additional metadata (not used in forward pass but available)
        metas = batch_data["metas"]

        # Forward pass through the classification model
        # Model processes point cloud + grasp points and returns loss + predictions
        loss, _ = self.model(pc, grasps, cls_target=success_labels, compute_loss=True)

        # Log training loss for monitoring
        self.log("loss", loss, sync_dist=True)
        return loss

    def validation_step(self, batch_data, batch_idx):
        """
        Single validation step with prediction caching for metrics
        
        Args:
            batch_data (dict): Same structure as training batch
            batch_idx (int): Batch index
            
        Returns:
            None (predictions cached for epoch-end metrics computation)
            
        PROCESS:
        1. Same data reshaping as training
        2. Forward pass to get predictions
        3. Convert probabilities to binary predictions using threshold
        4. Cache predictions and targets for epoch-end metrics
        """

        # Extract inputs (same as training)
        pc = batch_data["pc"]  # [B, N, 3]
        grasps = batch_data["grasps"]  # [B, num_grasps, 6]

        # Flatten and reshape (same as training)
        # TODO: verify this reshape consistency across different datasets
        success_labels = batch_data["success"].view(-1)  # [B*num_grasps]
        num_grasps = grasps.shape[1]

        # Create 1-1 pairing (same as training)
        pc = pc.repeat_interleave(num_grasps, dim=0)  # [B*num_grasps, N, 3]
        grasps = einops.rearrange(grasps, "b n c d -> (b n) c d")  # [B*num_grasps, 6]

        # Metadata
        metas = batch_data["metas"]

        # Forward pass with loss computation
        loss, preds = self.model(
            pc, grasps, cls_target=success_labels, compute_loss=True
        )

        # Convert probabilities to binary predictions using threshold
        preds = preds.detach()  # [B*num_grasps] - Probabilities from sigmoid
        preds[preds > self.CLS_PRED_THRESHOLD] = 1  # Above threshold → positive
        preds[preds <= self.CLS_PRED_THRESHOLD] = 0  # Below threshold → negative

        # Cache predictions and targets for epoch-end metrics computation
        # These will be concatenated across all validation batches
        self._update_cache("validation", "epoch", "cls_preds", preds.long())
        self._update_cache("validation", "epoch", "cls_targets", success_labels.long())

        # Log validation loss with progress bar display
        self.log("val_loss", loss, sync_dist=True, prog_bar=True)
        return

    def on_validation_epoch_end(self) -> None:
        """
        Compute and log validation metrics at the end of each validation epoch
        
        PROCESS:
        1. Collect all cached predictions and targets from validation batches
        2. Compute binary classification metrics
        3. Log metrics dictionary and accuracy to progress bar
        
        METRICS COMPUTED:
        - Accuracy: (TP + TN) / (TP + TN + FP + FN)
        - Precision: TP / (TP + FP)
        - Recall: TP / (TP + FN)
        - F1-score: 2 * (Precision * Recall) / (Precision + Recall)
        - Average Precision: Area under precision-recall curve
        """
        # Compute metrics from cached predictions
        eval_metrics = self._compute_metrics()
        
        # Log all metrics as a dictionary
        self.log_dict({"validation_metrics": eval_metrics}, sync_dist=True)
        
        # Log accuracy separately for progress bar display
        self.log(
            "val_accuracy", eval_metrics["accuracy"], prog_bar=True, sync_dist=True
        )
        return

    def _get_callbacks(self) -> list:
        """
        Define custom callbacks for training monitoring and checkpointing
        
        Returns:
            list: List of PyTorch Lightning callbacks
            
        CALLBACKS:
        1. ModelCheckpoint (top-3): Saves best 3 models based on training loss
        2. ModelCheckpoint (best): Saves single best model
        3. LearningRateMonitor: Logs learning rate changes
        """

        # Checkpoint callback: save top 3 models based on training loss
        checkpoint_callback1 = ModelCheckpoint(
            save_top_k=3,
            monitor="loss",
            mode="min",
            dirpath=self._experiment.ckpt_dir,
            filename="epoch_{epoch:02d}-step_{step}-loss_{loss:.2f}",
            save_last=True,
            every_n_train_steps=self._checkpointing_freq,
        )

        # Checkpoint callback: save single best model
        checkpoint_callback2 = ModelCheckpoint(
            save_top_k=1,
            monitor="loss",
            mode="min",
            dirpath=self._experiment.ckpt_dir,
            filename="best",
            save_last=True,
            every_n_train_steps=1000,
        )

        # Learning rate monitoring for debugging and analysis
        lr_monitor_callback = LearningRateMonitor(logging_interval="step")

        callbacks = [checkpoint_callback1, checkpoint_callback2, lr_monitor_callback]

        return callbacks

    def _get_logger(self) -> Logger:
        """
        Configure logging backend for experiment tracking
        
        Returns:
            Logger: Configured PyTorch Lightning logger
            
        SUPPORTED LOGGERS:
        - WandbLogger: For Weights & Biases integration
        - TensorBoardLogger: For TensorBoard visualization  
        - CSVLogger: Default fallback for simple CSV logging
        """
        if hasattr(self.trainer_config, "logger"):
            logger_config = self.trainer_config.logger

            if logger_config.type == "WandbLogger":
                assert hasattr(
                    logger_config, "project"
                ), "WandbLogger requires a project name to be specified in the config."

                logger = WandbLogger(
                    name=self._experiment.name,
                    project=logger_config.project,
                    save_dir=self._experiment.log_dir,
                    config=self._config,
                )
            elif logger_config.type == "TensorBoardLogger":
                logger = TensorBoardLogger(
                    save_dir=self._experiment.log_dir,
                    name=self._experiment.name,
                )
        else:
            # Default to CSV logging if no logger specified
            logger = CSVLogger(
                save_dir=self._experiment.log_dir,
                name=self._experiment.name,
            )
        return logger

    def _compute_metrics(self):
        """
        Compute binary classification metrics from cached predictions
        
        Returns:
            dict: Dictionary containing computed metrics
            
        METRICS:
        - accuracy: Overall classification accuracy
        - precision: Positive predictive value
        - recall: True positive rate (sensitivity)
        - f1: Harmonic mean of precision and recall
        - aP: Average precision (area under PR curve)
        
        INPUT SHAPES:
        - cls_preds: [total_samples] - Binary predictions (0 or 1)
        - cls_targets: [total_samples] - Binary ground truth labels (0 or 1)
        """

        # Collect all predictions and targets from validation epoch cache
        cls_preds = torch.cat(self._validation_cache["epoch"]["cls_preds"])  # [total_samples]
        cls_targets = torch.cat(self._validation_cache["epoch"]["cls_targets"])  # [total_samples]

        # Compute comprehensive binary classification metrics
        metrics = dict(
            accuracy=Metrics.binary_accuracy(cls_preds, cls_targets),
            precision=Metrics.binary_precision(cls_preds, cls_targets),
            recall=Metrics.binary_recall(cls_preds, cls_targets),
            f1=Metrics.binary_f1_score(cls_preds, cls_targets),
            aP=Metrics.binary_auprc(cls_preds, cls_targets),  # Average Precision
            # confusion_matrix=Metrics.binary_confusion_matrix(cls_preds, cls_targets),
        )

        return metrics
