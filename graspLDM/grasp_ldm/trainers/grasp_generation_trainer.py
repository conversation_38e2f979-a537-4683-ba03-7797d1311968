import os
import random
import warnings
from abc import abstractmethod, abstractproperty

import torch
import torcheval.metrics.functional as Metrics
from pytorch_lightning.callbacks import LearningRateMonitor, ModelCheckpoint
from pytorch_lightning.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ogger
from utils.rotations import tmrp_to_H

from grasp_ldm.dataset.builder import build_dataset_from_cfg
from grasp_ldm.models.builder import build_model_from_cfg
from grasp_ldm.utils.config import Config
from grasp_ldm.utils.torch_utils import fix_state_dict_prefix

from .experiment import Experiment
from .trainer import LightningTrainer, default


class GraspGenerationTrainer(LightningTrainer):
    """
    Abstract Base Trainer for Grasp Generation Models
    
    =============================================================================
    TWO-STAGE GRASP GENERATION ARCHITECTURE OVERVIEW
    =============================================================================
    
    This trainer supports a two-stage generative approach for robotic grasp synthesis:
    
    STAGE 1: Conditional Variational Autoencoder (CVAE) - GraspVAETrainer
    ====================================================================
    
    Purpose: Learn a compact latent representation of grasps conditioned on point clouds
    
    Architecture:
    - Point Cloud Encoder: PC [B, N, 3] → z_pc [B, pc_latent_dims]
    - Grasp Encoder: Grasp [B*G, 6/7] → z_grasp [B*G, grasp_latent_dims]
    - Grasp Decoder: (z_grasp, z_pc) → Reconstructed Grasp [B*G, 6/7]
    
    Training Data Flow:
    1. Input: pc [B, N, 3], grasps [B*G, 6/7] (with optional qualities)
    2. Encoding: pc → z_pc, grasps → (μ, σ, z_grasp) via reparameterization
    3. Decoding: (z_grasp, z_pc) → reconstructed grasps
    4. Loss: reconstruction + KL divergence + optional classification/quality losses
    
    Generation: Sample z_grasp ~ N(0,I), decode with point cloud conditioning
    
    STAGE 2: Latent Diffusion Model (LDM) - GraspLDMTrainer  
    =======================================================
    
    Purpose: Learn to generate diverse, high-quality grasps in VAE latent space
    
    Architecture:
    - Frozen VAE (from Stage 1): Provides latent space and conditioning
    - Diffusion Model: Learns to denoise in grasp latent space
    - Point Cloud Conditioning: Uses VAE's point cloud encoder
    
    Training Data Flow:
    1. Input: pc [B, N, 3], grasps [B*G, 6/7]
    2. VAE Encoding (frozen): pc → z_pc, grasps → z_grasp
    3. Diffusion Training: Add noise to z_grasp, train denoiser
    4. Loss: Denoising loss in latent space
    
    Generation: 
    1. Encode point cloud: pc → z_pc
    2. Sample noise and iteratively denoise: noise → z_grasp_clean
    3. Decode with VAE: (z_grasp_clean, z_pc) → final grasps
    
    =============================================================================
    COMMON DATA STRUCTURES AND DIMENSIONS
    =============================================================================
    
    Input Batch Structure:
    - pc: [B, N, 3] - Point cloud coordinates (B batches, N points, 3D)
    - grasps: [B, G, 6/7] - Grasp poses per point cloud (G grasps, 6/7 DOF)
    - qualities: [B, G, Q] - Optional grasp quality scores (Q quality metrics)
    - metas: dict - Normalization parameters and other metadata
    
    Reshaped for Training:
    - pc: [B, N, 3] (unchanged for encoding)
    - grasps: [B*G, 6/7] - Flattened for 1-to-1 processing
    - qualities: [B*G, Q] - Flattened to match grasps
    
    Latent Representations:
    - z_pc: [B, pc_latent_dims] - Point cloud features (conditioning)
    - z_grasp: [B*G, grasp_latent_dims] - Grasp latent codes
    - For diffusion: z_grasp reshaped to [B*G, 1, grasp_latent_dims]
    
    Output Dimensions:
    - Generated grasps: [B*num_grasps, 6/7] - Final grasp poses
    - Classifications: [B*num_grasps, 1] - Success probabilities
    - Qualities: [B*num_grasps, Q] - Quality scores (optional)
    
    =============================================================================
    TRAINING PIPELINE COMPARISON
    =============================================================================
    
    VAE Training (Stage 1):
    - Focus: Learn meaningful grasp representations
    - Loss: Reconstruction + KL divergence + optional auxiliary losses
    - Output: Trained encoder/decoder for grasp latent space
    
    LDM Training (Stage 2):
    - Focus: Learn to generate diverse grasps in latent space
    - Loss: Denoising loss in VAE latent space
    - Requires: Pre-trained frozen VAE from Stage 1
    - Output: Diffusion model for high-quality grasp generation
    
    =============================================================================
    """
    
    def __init__(
        self,
        config: Config = None,
        skip_validation: bool = False,
    ):
        """
        Initialize Grasp Generation Trainer
        
        Args:
            config (Config): Configuration containing:
                - model: Model configuration (VAE or LDM specific)
                - data: Dataset configuration for grasp generation
                - trainer: Training configuration
            skip_validation (bool): Whether to skip validation during training
            
        The trainer sets up:
        1. Experiment management with model-specific naming
        2. Model building (VAE or LDM via subclass implementation)
        3. Dataset building (typically AcronymShapenetPointclouds)
        4. Parent LightningTrainer initialization
        """

        # Split main sub-configs for better organization
        model_config = config.model
        data_config = config.data
        trainer_config = config.trainer

        # Experiment management with model-specific naming
        self._config = config
        self._experiment = Experiment(
            config.filename, model_suffix=self._model_type_str
        )

        # Checkpointing configuration
        # Default to saving checkpoints every 1000 steps if not specified
        self._checkpointing_freq = (
            trainer_config.checkpointing_freq
            if hasattr(trainer_config, "checkpointing_freq")
            else 1000
        )
        trainer_config.default_root_dir = self._experiment.ckpt_dir

        # Initialize parent trainer class with all configurations
        # Note: skip_validation=True for generation tasks (no standard validation metrics)
        super().__init__(
            model_config=model_config,
            data_config=data_config,
            trainer_config=trainer_config,
            skip_validation=skip_validation,
        )

        # Set up checkpoint resuming if available
        self.resume_from_checkpoint = self._experiment.default_resume_checkpoint

    @abstractproperty
    def _model_type_str(self) -> str:
        """
        Model type identifier for experiment naming and logging
        
        Returns:
            str: Model type string ("vae" or "ddm")
        """
        raise NotImplementedError

    @abstractproperty
    def _use_qualities(self) -> bool:
        """
        Whether the model uses grasp quality prediction
        
        Returns:
            bool: True if model predicts grasp qualities, False otherwise
        """
        raise NotImplementedError

    @abstractmethod
    def generate_grasps(self, pc, metas):
        """
        Abstract method for grasp generation from point clouds
        
        Args:
            pc (torch.Tensor): Point cloud coordinates [B, N, 3]
            metas (dict): Metadata including normalization parameters
            
        Returns:
            Generated grasps (implementation-specific format)
        """
        raise NotImplementedError

    def _build_dataset(self, data_config, split):
        """
        Build dataset for grasp generation training
        
        Args:
            data_config: Dataset configuration
            split (str): Dataset split ('train', 'val', 'test')
            
        Returns:
            Dataset: Built dataset with grasp generation data
            
        The dataset typically returns:
        - pc: [N, 3] point cloud coordinates
        - grasps: [G, 6/7] grasp poses (translation + rotation [+ quality])
        - qualities: [G, Q] grasp quality scores (optional)
        - metas: normalization and other metadata
        """
        dataset = build_dataset_from_cfg(data_config, split)

        # Pre-load any necessary data before spawning workers
        # This typically loads grasp data, point clouds, normalization params, etc.
        dataset.pre_load()

        return dataset

    def _build_model(self, model_config):
        """
        Abstract method for building the generation model
        
        Args:
            model_config: Model configuration
            
        Returns:
            nn.Module: Built model (VAE or LDM)
            
        Implementation varies by subclass:
        - GraspVAETrainer: Builds GraspCVAE
        - GraspLDMTrainer: Builds GraspLatentDDM with VAE dependency
        """
        raise NotImplementedError

    def training_step(self, batch_data, batch_idx):
        """
        Single training step for grasp generation models
        
        Args:
            batch_data (dict): Batch containing:
                - pc: [B, N, 3] point clouds
                - grasps: [B, G, 6/7] grasp poses
                - qualities: [B, G, Q] quality scores (optional)
                - metas: metadata dictionary
            batch_idx (int): Batch index
            
        Returns:
            torch.Tensor: Total training loss
            
        DIMENSION FLOW:
        1. Input: pc [B, N, 3], grasps [B, G, 6/7], qualities [B, G, Q]
        2. Reshape: grasps → [B*G, 6/7], qualities → [B*G, Q] (flatten for processing)
        3. Optional concatenation: grasps + qualities → [B*G, 6/7+Q]
        4. Model forward: → loss_dict with multiple loss components
        5. Return: total loss (scalar)
        """

        # Extract inputs from batch
        pc = batch_data["pc"]  # [B, N, 3] - Point cloud coordinates
        grasps_in = batch_data["grasps"]  # [B, G, 6/7] - Grasp poses
        metas = batch_data["metas"]  # Metadata dictionary
        # self.visualize_inputs(pc, grasps_in, batch_data["metas"])
        # Optional: concatenate grasp qualities if the model uses them
        if self._use_qualities:
            grasp_qualities = batch_data["qualities"]  # [B, G, Q] - Quality scores
            # Concatenate along last dimension: [B, G, 6/7] + [B, G, Q] → [B, G, 6/7+Q]
            grasps_in = torch.concatenate((grasps_in, grasp_qualities), -1)

        # Flatten grasps for model processing: [B, G, 6/7+Q] → [B*G, 6/7+Q]
        grasps_in = grasps_in.view(-1, grasps_in.shape[-1])

        # Update any hyperparameters that are scheduled (e.g., KL annealing)
        self._update_scheduled_hyperparams()

        # Optional kwargs for model-specific post-processing
        kwargs = {"metas": metas}

        # Forward pass through the generation model
        # Returns: model outputs (or None), loss dictionary
        _, loss_dict = self.model(pc, grasps_in, compute_loss=True, **kwargs)

        # Log all loss components for monitoring
        self.log_dict({key: val for key, val in loss_dict.items()}, sync_dist=True)

        # Return total loss for backpropagation
        return loss_dict.loss

    def validation_step(self, batch_data, batch_idx):
        """
        Validation step for grasp generation models
        
        Args:
            batch_data (dict): Same structure as training batch
            batch_idx (int): Batch index
            
        Returns:
            None
            
        PROCESS:
        1. Randomly select one batch per epoch for generation
        2. Generate grasps from point clouds using trained model
        3. Cache results for potential metrics computation
        
        Note: Standard validation metrics are not well-defined for generation tasks,
        so this primarily serves as a sanity check and for visualization.
        """
        # Get validation cache for this epoch
        val_cache = self._get_cache(mode="validation", type="batch")

        # Randomly select one validation batch per epoch for generation testing
        if "val_batch_idx" not in val_cache:
            self._update_cache(
                mode="validation",
                type="epoch",
                key="val_batch_idx",
                value=random.randint(0, len(self._val_dataloader) - 1),
            )

        # Only process the randomly selected batch
        if batch_idx == val_cache["val_batch_idx"]:
            pc = batch_data["pc"]  # [B, N, 3] - Point clouds for generation
            # grasps_in = batch_data["grasps"]
            metas = batch_data["metas"]  # Metadata for normalization

            # Generate grasps using the trained model
            # Output format depends on model type (VAE vs LDM)
            grasps = self.generate_grasps(pc, metas)

            # TODO: Implement generation quality metrics
            # Potential metrics: diversity, success rate, collision checking
        return

    def on_validation_epoch_end(self) -> None:
        """
        Called at the end of validation epoch
        
        Currently no specific validation metrics are computed for generation tasks.
        This could be extended to include:
        - Grasp diversity metrics
        - Generated grasp quality assessment
        - Visualization of generated samples
        """
        return

    def _compute_metrics(self):
        """
        Compute validation metrics for generation models
        
        Returns:
            dict: Empty dict (no standard metrics for generation)
            
        Note: Generation quality is typically assessed through:
        - Simulation-based success rates
        - Real robot experiments
        - Diversity and coverage metrics
        - Visual inspection of generated samples
        """
        return

    def _update_scheduled_hyperparams(self):
        """
        Update hyperparameters that depend on training steps
        
        Common scheduled parameters:
        - KL divergence annealing weights (for VAE training)
        - Learning rate schedules
        - Diffusion noise schedules
        
        Implementation varies by model type and is handled in subclasses.
        """
        return

    def _get_callbacks(self) -> list:
        """
        Define custom callbacks for generation model training
        
        Returns:
            list: List of PyTorch Lightning callbacks
            
        CALLBACKS:
        1. ModelCheckpoint (top-3): Saves best 3 models based on training loss
        2. ModelCheckpoint (best): Saves single best model (weights only)
        3. LearningRateMonitor: Logs learning rate changes
        """

        # Checkpoint callback: save top 3 models based on training loss
        checkpoint_callback1 = ModelCheckpoint(
            save_top_k=3,
            monitor="loss",
            mode="min",
            dirpath=self._experiment.ckpt_dir,
            filename="epoch_{epoch:02d}-step_{step}-loss_{loss:.2f}",
            save_last=True,
            every_n_train_steps=self._checkpointing_freq,
        )

        # Checkpoint callback: save single best model (weights only for efficiency)
        checkpoint_callback2 = ModelCheckpoint(
            save_top_k=1,
            monitor="loss",
            mode="min",
            dirpath=self._experiment.ckpt_dir,
            filename="best",
            save_weights_only=True,
            every_n_train_steps=self._checkpointing_freq,
        )

        # Learning rate monitoring for debugging and analysis
        lr_monitor_callback = LearningRateMonitor(logging_interval="step")

        callbacks = [checkpoint_callback1, checkpoint_callback2, lr_monitor_callback]

        return callbacks

    def _get_logger(self) -> Logger:
        """
        Configure logging backend for experiment tracking
        
        Returns:
            Logger: Configured PyTorch Lightning logger
            
        SUPPORTED LOGGERS:
        - WandbLogger: For Weights & Biases integration (with model type prefix)
        - TensorBoardLogger: For TensorBoard visualization
        - CSVLogger: Default fallback for simple CSV logging
        """
        if hasattr(self.trainer_config, "logger"):
            logger_config = self.trainer_config.logger

            if logger_config.type == "WandbLogger":
                assert hasattr(
                    logger_config, "project"
                ), "WandbLogger requires a project name to be specified in the config."

                # Include model type in experiment name for better organization
                logger = WandbLogger(
                    name=f"{self._model_type_str.upper()}_{self._experiment.name}",
                    project=logger_config.project,
                    save_dir=self._experiment.log_dir,
                    config=self._config,
                )
            elif logger_config.type == "TensorBoardLogger":
                logger = TensorBoardLogger(
                    save_dir=self._experiment.log_dir,
                    name=self._experiment.name,
                )
        else:
            # Default to CSV logging if no logger specified
            logger = CSVLogger(
                save_dir=self._experiment.log_dir,
                name=self._experiment.name,
            )
        return logger


class GraspVAETrainer(GraspGenerationTrainer):
    """
    Trainer for Conditional Variational Autoencoder (CVAE) - Stage 1 of Generation Pipeline
    
    =============================================================================
    VAE TRAINING PIPELINE
    =============================================================================
    
    Purpose: Learn a compact, meaningful latent representation of grasps conditioned on point clouds
    
    Model: GraspCVAE (Conditional Variational Autoencoder)
    Architecture:
    - Point Cloud Encoder: PVCNN-based, extracts geometric features
    - Grasp Encoder: Encodes grasp poses conditioned on point cloud features
    - VAE Bottleneck: Reparameterization trick for probabilistic latent space
    - Grasp Decoder: Reconstructs grasps conditioned on point cloud features
    
    TRAINING DATA FLOW:
    ===================
    
    Input Batch:
    - pc: [B, N, 3] - Point cloud coordinates
    - grasps: [B*G, 6/7] - Flattened grasp poses (translation + rotation [+ quality])
    
    Encoding Phase:
    1. Point Cloud Encoding: pc [B, N, 3] → z_pc [B, pc_latent_dims]
    2. Grasp Encoding: grasps [B*G, 6/7] → z_grasp [B*G, grasp_latent_dims]
    3. VAE Bottleneck: z_grasp → (μ, logvar) → reparameterized z_grasp
    
    Decoding Phase:
    4. Conditional Decoding: (z_grasp, z_pc) → reconstructed_grasps [B*G, 6/7]
    
    Loss Computation:
    5. Reconstruction Loss: ||grasps_in - reconstructed_grasps||²
    6. KL Divergence: D_KL(q(z|x) || p(z)) where p(z) = N(0,I)
    7. Optional: Classification loss, Quality loss
    
    GENERATION PIPELINE:
    ====================
    
    Input: pc [B, N, 3], num_grasps (int)
    
    Process:
    1. Encode point cloud: pc → z_pc [B, pc_latent_dims]
    2. Sample grasp latents: z_grasp ~ N(0,I) [B*num_grasps, grasp_latent_dims]
    3. Repeat point cloud conditioning: z_pc [B*num_grasps, pc_latent_dims]
    4. Decode: (z_grasp, z_pc) → generated_grasps [B*num_grasps, 6/7]
    
    Output: List of tensors [grasps, classifications, qualities]
    
    =============================================================================
    """
    
    def __init__(self, config: Config = None):
        """
        Initialize VAE trainer for Stage 1 grasp generation
        
        Args:
            config (Config): Configuration with VAE-specific model settings
            
        Note: Validation is skipped as generation quality is better assessed
        through simulation or real robot experiments.
        """
        super().__init__(config, skip_validation=True)

    @property
    def _model_type_str(self):
        """Model type identifier for VAE training"""
        return "vae"

    @property
    def _use_qualities(self):
        """Check if the VAE model predicts grasp qualities"""
        return self.model.use_grasp_qualities

    def generate_grasps(self, pc, metas):
        """
        Generate grasps using trained VAE model
        
        Args:
            pc (torch.Tensor): Point clouds [B, N, 3]
            metas (dict): Metadata for normalization
            
        Returns:
            List: Generated outputs from VAE decoder
                - grasps: [B*num_grasps, 6] - Generated grasp poses
                - classifications: [B*num_grasps, 1] - Success probabilities
                - qualities: [B*num_grasps, Q] - Quality scores (optional)
        """
        grasps = self.model.generate_grasps(pc, metas)
        return grasps

    def _build_model(self, model_config):
        """
        Build GraspCVAE model for Stage 1 training
        
        Args:
            model_config: Model configuration containing VAE settings
            
        Returns:
            GraspCVAE: Conditional Variational Autoencoder model
            
        The model includes:
        - Point cloud encoder (PVCNN-based)
        - Conditional grasp encoder
        - VAE bottleneck with reparameterization
        - Conditional grasp decoder
        - Multiple loss functions (reconstruction, KL, optional classification/quality)
        """
        model = build_model_from_cfg(model_config.vae)

        # Set up checkpoint resuming if VAE checkpoint is specified
        if model_config.vae.ckpt_path is not None:
            assert os.path.exists(
                model_config.vae.ckpt_path
            ), f"Checkpoint {model_config.vae.ckpt_path} does not exist."
            self.resume_from_checkpoint = model_config.vae.ckpt_path

        return model

    def _update_scheduled_hyperparams(self):
        """
        Update KL divergence annealing weights during VAE training
        
        KL annealing gradually increases the weight of KL divergence loss
        to prevent posterior collapse and improve training stability.
        
        Schedule: KL weight starts at 0 and gradually increases to target value
        """
        if not hasattr(self.model, "latent_losses"):
            warnings.warn(
                "Expected model to have latent losses for weight update while annealing is on."
            )
            return

        # Update KL divergence weights based on training step
        for loss_instance in self.model.latent_losses:
            if loss_instance.schedule is not None:
                loss_instance.set_weight_from_schedule(self.global_step)
                self.log(f"kl-{loss_instance.name}-weight", loss_instance.weight)
        return


class GraspLDMTrainer(GraspGenerationTrainer):
    """
    Trainer for Latent Diffusion Model (LDM) - Stage 2 of Generation Pipeline
    
    =============================================================================
    LDM TRAINING PIPELINE
    =============================================================================
    
    Purpose: Learn to generate diverse, high-quality grasps in VAE latent space
    
    Model: GraspLatentDDM (Latent Denoising Diffusion Model)
    Requirements: Pre-trained VAE from Stage 1 (frozen during training)
    
    Architecture:
    - Frozen VAE: Provides latent space and point cloud conditioning
    - Diffusion Model: U-Net or ResNet-based denoiser in latent space
    - Point Cloud Conditioning: Uses VAE's point cloud encoder features
    
    TRAINING DATA FLOW:
    ===================
    
    Input Batch:
    - pc: [B, N, 3] - Point cloud coordinates
    - grasps: [B*G, 6/7] - Flattened grasp poses
    
    VAE Encoding (Frozen):
    1. Point Cloud Encoding: pc [B, N, 3] → z_pc [B, pc_latent_dims]
    2. Grasp Encoding: grasps [B*G, 6/7] → z_grasp [B*G, grasp_latent_dims]
    3. Repeat conditioning: z_pc [B*G, pc_latent_dims] (match grasp batch size)
    
    Diffusion Training:
    4. Add noise: z_grasp + ε*σ → noisy_z_grasp [B*G, 1, grasp_latent_dims]
    5. Predict clean: diffusion_model(noisy_z_grasp, σ, z_pc) → pred_z_grasp
    6. Compute loss: ||pred_z_grasp - z_grasp||² (denoising loss)
    
    GENERATION PIPELINE:
    ====================
    
    Input: pc [B, N, 3], num_grasps (int)
    
    Process:
    1. Point Cloud Encoding: pc → z_pc [B, pc_latent_dims]
    2. Repeat conditioning: z_pc [B*num_grasps, pc_latent_dims]
    3. Initialize noise: z₀ ~ N(0,I) [B*num_grasps, 1, grasp_latent_dims]
    4. Iterative denoising: z₀ → z₁ → ... → z_clean (DDPM/DDIM sampling)
    5. VAE decoding: z_clean → generated_grasps [B*num_grasps, 6/7]
    
    Output: High-quality, diverse grasp poses
    
    =============================================================================
    TWO-STAGE TRAINING RELATIONSHIP:
    =============================================================================
    
    Stage 1 (VAE): Learn meaningful grasp representations
    - Focus: Reconstruction quality and latent space structure
    - Output: Trained encoder/decoder pair
    
    Stage 2 (LDM): Learn to generate in latent space
    - Focus: Diversity and quality of generated samples
    - Input: Frozen VAE from Stage 1
    - Output: Diffusion model for high-quality generation
    
    Benefits of two-stage approach:
    - Computational efficiency (diffusion in compact latent space)
    - Better sample quality (leverages VAE's learned representations)
    - Modular training (can improve each stage independently)
    
    =============================================================================
    """
    
    def __init__(self, config: Config = None):
        """
        Initialize LDM trainer for Stage 2 grasp generation
        
        Args:
            config (Config): Configuration with LDM-specific settings including:
                - ddm: Diffusion model configuration
                - vae: VAE model configuration (for loading frozen weights)
                - use_vae_ema_model: Whether to use EMA weights from VAE training
                
        Note: Validation is skipped as generation quality is better assessed
        through simulation or real robot experiments.
        """
        super().__init__(config, skip_validation=True)
        self._use_vae_ema_model_from_ckpt = config.model.ddm.use_vae_ema_model

    @property
    def _model_type_str(self):
        """Model type identifier for LDM training"""
        return "ddm"

    @property
    def _use_qualities(self):
        """Check if the underlying VAE model predicts grasp qualities"""
        return self.model.use_grasp_qualities

    def on_train_start(self) -> None:
        """
        Called when training begins - load and freeze VAE model
        
        This ensures the VAE weights are properly loaded before diffusion training starts.
        Critical for two-stage training pipeline.
        """
        self._load_and_freeze_vae_model()
        return

    def generate_grasps(self, pc, metas):
        """
        Generate grasps using trained LDM + frozen VAE
        
        Args:
            pc (torch.Tensor): Point clouds [B, N, 3]
            metas (dict): Metadata for normalization
            
        Returns:
            List: Generated outputs from complete LDM pipeline
                - grasps: [B*num_grasps, 6] - Generated grasp poses
                - classifications: [B*num_grasps, 1] - Success probabilities  
                - qualities: [B*num_grasps, Q] - Quality scores (optional)
                
        Pipeline:
        1. Point cloud encoding (VAE encoder)
        2. Latent diffusion sampling
        3. VAE decoding to final grasps
        """
        grasps = self.model.generate_grasps(pc, metas)
        return grasps

    def _build_model(self, model_config):
        """
        Build GraspLatentDDM model for Stage 2 training
        
        Args:
            model_config: Model configuration containing:
                - ddm: Diffusion model settings
                - vae: VAE model settings (for building frozen component)
                
        Returns:
            GraspLatentDDM: Latent Diffusion Model with embedded VAE
            
        The model includes:
        - Diffusion model (U-Net or ResNet-based denoiser)
        - Frozen VAE model (encoder/decoder from Stage 1)
        - Point cloud conditioning mechanism
        """
        # Build the main diffusion model
        model = build_model_from_cfg(model_config.ddm)
        
        # Build and attach the VAE model (will be frozen)
        model.set_vae_model(build_model_from_cfg(model_config.vae))

        # Set up checkpoint resuming for diffusion model if specified
        cfg_ddm_ckpt_path = model_config.ddm.ckpt_path
        if cfg_ddm_ckpt_path is not None:
            assert os.path.exists(
                cfg_ddm_ckpt_path
            ), f"Checkpoint {cfg_ddm_ckpt_path} does not exist."
            self.resume_from_checkpoint = cfg_ddm_ckpt_path

        # Configure VAE weight loading (standard vs EMA weights)
        if default(model_config.ddm.use_vae_ema_model, False):
            self._use_vae_ema_model_from_ckpt = True

        # Freeze VAE model parameters to prevent updates during diffusion training
        model.freeze_vae_model()

        return model

    def _load_and_freeze_vae_model(self):
        """
        Load pre-trained VAE weights and freeze the model for diffusion training
        
        Loading Priority:
        1. If diffusion checkpoint exists: VAE weights included in checkpoint
        2. If VAE checkpoint specified in config: Load from specified path
        3. Default: Load from experiment's VAE checkpoint directory
        
        Weight Types:
        - Standard weights: Final model weights from VAE training
        - EMA weights: Exponential moving average weights (often better quality)
        """

        # Only load VAE weights if no diffusion checkpoint is being resumed
        if self.resume_from_checkpoint is None:
            cfg_vae_ckpt_path = self._model_config.vae.ckpt_path
            
            if cfg_vae_ckpt_path is not None:
                # Use explicitly specified VAE checkpoint
                assert os.path.exists(
                    cfg_vae_ckpt_path
                ), f"Checkpoint {cfg_vae_ckpt_path} does not exist."
            else:
                # Use default VAE checkpoint from experiment directory
                cfg_vae_ckpt_path = (
                    f"{self._experiment.exp_dir}/vae/checkpoints/last.ckpt"
                )

            # Load VAE state dictionary from checkpoint
            state_dict = torch.load(cfg_vae_ckpt_path)["state_dict"]
            
            if self._use_vae_ema_model_from_ckpt:
                # Load EMA (Exponential Moving Average) weights for better quality
                state_dict = fix_state_dict_prefix(
                    state_dict, "ema_model.online_model", ignore_all_others=True
                )
            else:
                # Load standard model weights
                state_dict = fix_state_dict_prefix(
                    state_dict, "model", ignore_all_others=True
                )
                
            # Load weights into the VAE component of the diffusion model
            self.model.load_vae_weights(state_dict=state_dict)

        return
