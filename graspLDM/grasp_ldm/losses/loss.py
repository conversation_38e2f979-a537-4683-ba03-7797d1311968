"""
Loss Functions for GraspLDM

This module contains various loss functions used in the GraspLDM framework for training
Variational Autoencoders (VAE) and Latent Diffusion Models (LDM) for robotic grasp generation.

The loss functions are organized into several categories:
1. VAE Losses: Reconstruction and KL divergence losses for variational autoencoders
2. Grasp-specific Losses: Specialized losses for grasp pose estimation and control points
3. Classification Losses: Binary classification for grasp success prediction
4. Quality Losses: Regression losses for grasp quality estimation

Key Features:
- Support for cyclical annealing in KL divergence loss
- Weighted reconstruction losses for translation and rotation components
- Control points-based reconstruction for gripper geometry
- Smooth L1 loss for robust quality estimation

"""

import json
import random
from typing import Any, Tuple, Optional

import numpy as np
import torch
from torch import nn

from grasp_ldm.utils.rotations import tmrp_to_H

__all__ = [
    "VAEReconstructionLoss",
    "VAELatentLoss",
    "ClassificationLoss",
    "QualityLoss",
    "GraspReconstructionLoss",
    "GraspControlPointsReconstructionLoss",
]


# From: https://github.com/haofuml/cyclical_annealing
def linear_cyclical_anneling(n_iter, start=0.0, stop=1.0, n_cycle=4, ratio=0.5):
    """
    Generate a linear cyclical annealing schedule for KL divergence loss weighting.
    
    This function creates a schedule that linearly increases from start to stop value
    over a fraction of each cycle, then remains at the stop value for the rest of the cycle.
    This helps with posterior collapse in VAE training by gradually increasing the KL weight.
    
    Pipeline:
    1. Initialize array with stop values
    2. For each cycle, create linear ramp from start to stop
    3. Apply ramp over the first 'ratio' portion of each cycle
    
    Args:
        n_iter (int): Total number of training iterations
        start (float): Starting weight value (typically close to 0)
        stop (float): Maximum weight value 
        n_cycle (int): Number of annealing cycles
        ratio (float): Fraction of each cycle used for linear increase (0-1)
        
    Returns:
        np.ndarray: Array of shape [n_iter] containing weight schedule
        
    Dimension Flow:
        Input: scalar parameters
        Output: [n_iter] - weight for each training step
    """
    L = np.ones(n_iter) * stop  # Initialize with stop values: [n_iter]
    period = n_iter / n_cycle   # Length of each cycle
    step = (stop - start) / (period * ratio)  # Linear schedule increment

    for c in range(n_cycle):
        v, i = start, 0
        # Create linear ramp for first 'ratio' portion of cycle
        while v <= stop and (int(i + c * period) < n_iter):
            L[int(i + c * period)] = v
            v += step
            i += 1
    return L


class VAEReconstructionLoss(nn.Module):
    """
    Basic reconstruction loss for Variational Autoencoders using Mean Squared Error.
    
    This is the fundamental reconstruction term in the VAE objective that measures
    how well the decoder can reconstruct the input from the latent representation.
    
    Pipeline:
    1. Compute MSE between input and reconstructed output
    2. Apply weight scaling
    
    Mathematical Form:
        L_recon = weight * MSE(input, output)
        L_recon = weight * (1/N) * Σ(input - output)²
    """
    
    def __init__(self, weight=1, name="reconstruction_loss") -> None:
        """
        Initialize VAE reconstruction loss.
        
        Args:
            weight (float): Scaling factor for the loss term
            name (str): Name identifier for the loss
        """
        super().__init__()
        self.name = name
        self.criterion = nn.MSELoss()  # Mean Squared Error criterion
        self.weight = weight

    def forward(self, input: torch.Tensor, output: torch.Tensor) -> torch.Tensor:
        """
        Compute weighted MSE reconstruction loss.
        
        Args:
            input (torch.Tensor): Ground truth data [B, D] where B=batch_size, D=data_dim
            output (torch.Tensor): Reconstructed data [B, D]
            
        Returns:
            torch.Tensor: Scalar weighted reconstruction loss [1]
            
        Dimension Flow:
            input: [B, D] -> MSE -> [1] -> weight * [1] -> [1]
            output: [B, D] ----^
        """
        return self.weight * self.criterion(input, output)


class GraspReconstructionLoss(VAEReconstructionLoss):
    """
    Specialized reconstruction loss for grasp poses with separate weighting for translation and rotation.
    
    This loss function handles 6DOF grasp poses represented as [translation(3), MRP(3)] where
    MRP is Modified Rodrigues Parameters for rotation. Different weights are applied to 
    translation and rotation components to balance their contributions.
    
    Pipeline:
    1. Clone input tensors to avoid in-place modifications
    2. Apply translation_weight to first 3 dimensions (translation)
    3. Apply rotation_weight to last 3 dimensions (rotation) 
    4. Compute weighted MSE loss
    
    Mathematical Form:
        x_weighted = [t * w_trans, mrp * w_rot]
        L_grasp = MSE(x_weighted_gt, x_weighted_pred)
    """
    
    def __init__(
        self, translation_weight=10, rotation_weight=1, name="reconstruction_loss"
    ) -> None:
        """
        Initialize grasp reconstruction loss with component-specific weights.
        
        Args:
            translation_weight (float): Weight for translation components (typically higher)
            rotation_weight (float): Weight for rotation components  
            name (str): Name identifier for the loss
        """
        super().__init__(weight=1, name=name)
        self.translation_weight = translation_weight
        self.rotation_weight = rotation_weight

    def forward(self, x_out: torch.Tensor, x_in: torch.Tensor, **kwargs) -> torch.Tensor:
        """
        Compute weighted reconstruction loss for grasp poses.
        
        Args:
            x_out (torch.Tensor): Predicted pose [B, 6] - (translation(3), MRP(3))
            x_in (torch.Tensor): Ground truth pose [B, 6] - (translation(3), MRP(3))
            **kwargs: Additional arguments (unused)

        Returns:
            torch.Tensor: Scalar weighted reconstruction loss [1]
            
        Dimension Flow:
            x_out: [B, 6] -> clone -> [B, 6] -> weight components -> [B, 6] -> MSE -> [1]
            x_in:  [B, 6] -> clone -> [B, 6] -> weight components -> [B, 6] ----^
            
        Component Weighting:
            [:3] (translation): multiplied by translation_weight
            [3:] (rotation): multiplied by rotation_weight
        """
        # Clone tensors to avoid modifying original data
        x_pred = x_out.clone()  # [B, 6]
        x_pred[..., :3] = x_pred[..., :3] * self.translation_weight  # Weight translation
        x_pred[..., 3:] = x_pred[..., 3:] * self.rotation_weight     # Weight rotation

        x_gt = x_in.clone()     # [B, 6] 
        x_gt[..., :3] = x_gt[..., :3] * self.translation_weight      # Weight translation
        x_gt[..., 3:] = x_gt[..., 3:] * self.rotation_weight        # Weight rotation

        return super().forward(x_gt, x_pred)  # Compute MSE: [1]


class GraspControlPointsReconstructionLoss(VAEReconstructionLoss):
    """
    Advanced reconstruction loss based on gripper control points in 3D space.
    
    This loss function transforms grasp poses to 3D control points representing the gripper
    geometry and computes reconstruction loss in the control point space. This provides
    a more geometrically meaningful loss that directly relates to gripper positioning.
    
    Pipeline:
    1. Load gripper control points from JSON file
    2. Denormalize predicted and target poses using dataset statistics
    3. Convert 6DOF poses to 4x4 homogeneous transformation matrices
    4. Transform control points using the transformation matrices
    5. Compute MSE loss between transformed control points
    
    Mathematical Form:
        H = tmrp_to_H(pose_6dof)  # 6DOF -> 4x4 transformation matrix
        ctrl_pts_3d = H @ ctrl_pts_homogeneous  # Transform control points
        L_ctrl = MSE(ctrl_pts_target, ctrl_pts_pred)
    """
    
    def __init__(
        self,
        weight=1,
        name="reconstruction_loss", 
        control_pts_file="grasp_ldm/dataset/acronym/gripper_ctrl_pts.json",
    ) -> None:
        """
        Initialize control points reconstruction loss.
        
        Args:
            weight (float): Scaling factor for the loss term
            name (str): Name identifier for the loss
            control_pts_file (str): Path to JSON file containing gripper control points
        """
        super().__init__(weight=1, name=name)

        # Load gripper control points from file
        with open(control_pts_file) as f:
            _control_pts = np.array(json.load(f))  # [N_pts, 3]

        # Convert to homogeneous coordinates by appending 1s
        # This enables transformation using 4x4 matrices
        self.control_pts = torch.from_numpy(
            np.concatenate(
                [_control_pts, np.ones((_control_pts.shape[0], 1))],  # [N_pts, 3] -> [N_pts, 4]
                axis=1,
            )
        )
        self.criterion = nn.MSELoss()
        self.weight = weight

    def forward(self, x_target: torch.Tensor, x_pred: torch.Tensor, **kwargs) -> torch.Tensor:
        """
        Compute control points reconstruction loss.
        
        Args:
            x_target (torch.Tensor): Ground truth poses [B*N_grasps, 6] - normalized
            x_pred (torch.Tensor): Predicted poses [B*N_grasps, 6] - normalized  
            **kwargs: Must contain 'metas' with normalization statistics
                metas['grasp_std']: [B] - standard deviation for denormalization
                metas['grasp_mean']: [B] - mean for denormalization

        Returns:
            torch.Tensor: Scalar control points reconstruction loss [1]
            
        Dimension Flow:
            x_target: [B*N_grasps, 6] -> reshape -> [B, N_grasps, 6] -> denormalize -> [B, N_grasps, 6]
                      -> reshape -> [B*N_grasps, 6] -> tmrp_to_H -> [B*N_grasps, 4, 4]
                      -> transform ctrl_pts -> [B*N_grasps, N_pts, 4] -> [B*N_grasps, N_pts, 3]
            
            x_pred: [B*N_grasps, 6] -> ... (same pipeline) ... -> [B*N_grasps, N_pts, 3]
            
            Final: MSE([B*N_grasps, N_pts, 3], [B*N_grasps, N_pts, 3]) -> [1]
        """
        metas = kwargs["metas"]
        pc_batch_size = metas["grasp_std"].shape[0]  # B
        
        # Denormalize poses: normalized -> original scale
        # Reshape to separate batch and grasp dimensions: [B*N_grasps, 6] -> [B, N_grasps, 6]
        h_target = x_target.view((pc_batch_size, -1, 6)) * metas["grasp_std"].unsqueeze(1) + metas["grasp_mean"].unsqueeze(1)
        h_pred = x_pred.view((pc_batch_size, -1, 6)) * metas["grasp_std"].unsqueeze(1) + metas["grasp_mean"].unsqueeze(1)

        # Move control points to appropriate device and dtype
        ctrl_pts = self.control_pts.clone().to(h_target.device, h_target.dtype)  # [N_pts, 4]

        # Convert 6DOF poses to 4x4 homogeneous transformation matrices
        # Flatten batch and grasp dimensions: [B, N_grasps, 6] -> [B*N_grasps, 6]
        H_target = tmrp_to_H(h_target.view((-1, 6)))  # [B*N_grasps, 4, 4]
        H_pred = tmrp_to_H(h_pred.view((-1, 6)))      # [B*N_grasps, 4, 4]

        # Transform control points using the transformation matrices
        # Matrix multiplication: [B*N_grasps, 4, 4] @ [4, N_pts] -> [B*N_grasps, 4, N_pts]
        # Transpose to get: [B*N_grasps, N_pts, 4], then take first 3 coords: [B*N_grasps, N_pts, 3]
        control_pts_target = (H_target @ ctrl_pts.T).transpose(1, 2)[..., :3]  # [B*N_grasps, N_pts, 3]
        control_pts_pred = (H_pred @ ctrl_pts.T).transpose(1, 2)[..., :3]      # [B*N_grasps, N_pts, 3]

        # Get the control points
        # control_pts_target = (H_target @ ctrl_pts.T).transpose(1, 2)
        # control_pts_pred = (H_pred @ ctrl_pts.T).transpose(1, 2)
        
        return self.weight * self.criterion(control_pts_target, control_pts_pred)


class VAELatentLoss(nn.Module):
    """
    KL divergence loss for VAE latent space regularization with optional cyclical annealing.
    
    This loss encourages the learned latent distribution to be close to a standard normal
    distribution, preventing posterior collapse and ensuring meaningful latent representations.
    Supports cyclical annealing to gradually increase the KL weight during training.
    
    Pipeline:
    1. Compute KL divergence between learned distribution N(μ, σ²) and standard normal N(0, I)
    2. Apply weight (constant or from cyclical schedule)
    3. Optionally return both weighted and unweighted losses
    
    Mathematical Form:
        KL(q(z|x) || p(z)) = -0.5 * Σ(1 + log(σ²) - μ² - σ²)
        L_KL = weight * KL_divergence
    """
    
    def __init__(
        self,
        weight=1,
        name="kl_loss",
        cyclical_annealing=False,
        num_steps=None,
        num_cycles=None,
        start=1e-7,
        stop=0.2,
        ratio=0.25,
    ) -> None:
        """
        Initialize VAE latent loss with optional cyclical annealing.
        
        Args:
            weight (float): Constant weight (used if cyclical_annealing=False)
            name (str): Name identifier for the loss
            cyclical_annealing (bool): Whether to use cyclical weight annealing
            num_steps (int): Total training steps (required for annealing)
            num_cycles (int): Number of annealing cycles (required for annealing)
            start (float): Starting weight for annealing (typically very small)
            stop (float): Maximum weight for annealing
            ratio (float): Fraction of cycle used for linear increase
        """
        super().__init__()
        self.name = name

        if not cyclical_annealing:
            # Use constant weight
            self.weight = weight
            self.schedule = None
        else:
            # Generate cyclical annealing schedule
            assert num_cycles is not None and num_steps is not None
            self.weight = None
            self.schedule = linear_cyclical_anneling(
                num_steps,
                start=start,
                stop=stop,
                n_cycle=num_cycles,
                ratio=ratio,
            )  # [num_steps]
        self.is_annealed = cyclical_annealing

    def forward(
        self,
        mu: torch.Tensor,
        logvar: torch.Tensor,
        return_unweighted: bool = False,
        **kwargs,
    ) -> torch.Tensor:
        """
        Compute KL divergence loss for VAE latent variables.
        
        Args:
            mu (torch.Tensor): Latent means [B, D] where B=batch_size, D=latent_dim
            logvar (torch.Tensor): Latent log-variances [B, D]
            return_unweighted (bool): Whether to return both weighted and unweighted losses
            **kwargs: Additional arguments (unused)

        Returns:
            torch.Tensor: Weighted KL loss [1] (if return_unweighted=False)
            Tuple[torch.Tensor, torch.Tensor]: (weighted_loss[1], unweighted_loss[1]) (if return_unweighted=True)
            
        Dimension Flow:
            mu: [B, D] -> KL computation -> [B] -> mean -> [1]
            logvar: [B, D] ----^
            
        KL Divergence Computation:
            For each latent dimension: -0.5 * (1 + logvar - mu² - exp(logvar))
            Sum over dimensions: [B, D] -> [B]
            Mean over batch: [B] -> [1]
        """
        # Compute KL divergence: KL(N(μ,σ²) || N(0,I))
        # Formula: -0.5 * Σ(1 + log(σ²) - μ² - σ²)
        kl_d = -0.5 * torch.sum(1 + logvar - mu**2 - logvar.exp(), dim=1)  # [B, D] -> [B]
        kl_d = torch.mean(kl_d, dim=0)  # [B] -> [1]

        if return_unweighted:
            return self.weight * kl_d, kl_d  # ([1], [1])
        else:
            return self.weight * kl_d  # [1]

    def set_weight_from_schedule(self, step: int) -> None:
        """
        Update loss weight based on cyclical annealing schedule.
        
        Args:
            step (int): Current training step
            
        Raises:
            AssertionError: If no annealing schedule is available
        """
        assert (
            hasattr(self, "schedule") and self.schedule is not None
        ), f"No member schedule found in self, to set the loss weight from schedule. Weight annealing was set to {self.is_annealed}"

        # Use scheduled weight or last value if step exceeds schedule length
        self.weight = (
            self.schedule[step] if step < len(self.schedule) else self.schedule[-1]
        )
        return


class ClassificationLoss(nn.Module):
    """
    Binary classification loss for grasp success prediction using BCE with logits.
    
    This loss function is used to train models to predict whether a grasp will be
    successful or not. Uses BCE with logits for numerical stability.
    
    Pipeline:
    1. Apply sigmoid activation and compute binary cross-entropy
    2. Apply weight scaling
    
    Mathematical Form:
        L_cls = weight * BCE_with_logits(output, targets)
        L_cls = weight * (-targets * log(σ(output)) - (1-targets) * log(1-σ(output)))
    """
    
    def __init__(self, weight=1, name="classfication_loss") -> None:
        """
        Initialize binary classification loss.
        
        Args:
            weight (float): Scaling factor for the loss term
            name (str): Name identifier for the loss
        """
        super().__init__()
        self.name = name
        self.weight = weight
        self.class_criterion = nn.BCEWithLogitsLoss(reduction="mean")  # Numerically stable BCE
        self.class_weight = weight  # Duplicate for backward compatibility

    def forward(self, output: torch.Tensor, targets: torch.Tensor, **kwargs) -> torch.Tensor:
        """
        Compute binary classification loss.
        
        Args:
            output (torch.Tensor): Raw logits [B, 1] or [B] - model predictions before sigmoid
            targets (torch.Tensor): Binary targets [B, 1] or [B] - ground truth labels (0 or 1)
            **kwargs: Additional arguments (unused)

        Returns:
            torch.Tensor: Scalar weighted classification loss [1]
            
        Dimension Flow:
            output: [B] -> BCE_with_logits -> [1] -> weight * [1] -> [1]
            targets: [B] ----^
            
        Note: BCE with logits applies sigmoid internally for numerical stability
        """
        classification_loss = self.class_criterion(output, targets)  # [1]
        return self.weight * classification_loss  # [1]


class QualityLoss(nn.Module):
    """
    Regression loss for grasp quality estimation using Smooth L1 Loss.
    
    This loss function is used to train models to predict continuous grasp quality scores.
    Uses Smooth L1 (Huber) loss which is less sensitive to outliers than MSE.
    
    Pipeline:
    1. Compute Smooth L1 loss between predicted and target quality scores
    2. Apply weight scaling
    
    Mathematical Form:
        L_qual = weight * SmoothL1(quals_pred, quals_target)
        SmoothL1(x) = 0.5*x² if |x| < 1, else |x| - 0.5
    """
    
    def __init__(self, weight=1, name="quality_loss") -> None:
        """
        Initialize grasp quality regression loss.
        
        Args:
            weight (float): Scaling factor for the loss term
            name (str): Name identifier for the loss
        """
        super().__init__()
        self.name = name
        self.weight = weight
        self.criterion = nn.SmoothL1Loss()  # Robust to outliers

    def forward(self, quals_in: torch.Tensor, quals_target: torch.Tensor, **kwargs) -> torch.Tensor:
        """
        Compute quality regression loss.
        
        Args:
            quals_in (torch.Tensor): Predicted quality scores [B] or [B, 1]
            quals_target (torch.Tensor): Target quality scores [B] or [B, 1]
            **kwargs: Additional arguments (unused)

        Returns:
            torch.Tensor: Scalar weighted quality loss [1]
            
        Dimension Flow:
            quals_in: [B] -> SmoothL1 -> [1] -> weight * [1] -> [1]
            quals_target: [B] ----^
            
        Note: SmoothL1 is more robust to outliers than MSE, providing stable gradients
        """
        confidence_loss = self.criterion(quals_in, quals_target)  # [1]
        return self.weight * confidence_loss  # [1]
