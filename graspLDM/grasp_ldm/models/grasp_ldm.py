import warnings

import torch
from addict import Dict

from .diffusion import ElucidatedDiffusion, GaussianDiffusion1D
from .modules.base_network import BaseGraspSampler


class GraspLatentDDM(BaseGraspSampler):
    """
    Grasp Latent Denoising Diffusion Model - A two-stage generative model for grasp synthesis
    
    ARCHITECTURE OVERVIEW:
    ======================
    
    GraspLatentDDM = VAE (Conditional Variational Autoencoder) + Latent Diffusion Model
    
    Stage 1: VAE Training (separate, frozen during diffusion training)
    ----------------------------------------------------------------
    - Point Cloud Encoder: PC [B, N, 3] → z_pc [B, pc_latent_dims]
    - Grasp Encoder: Grasp [B*G, 6/7] → z_grasp [B*G, grasp_latent_dims] 
    - Grasp Decoder: (z_grasp, z_pc) → Reconstructed Grasp [B*G, 6/7]
    
    Stage 2: Latent Diffusion Training (this model)
    -----------------------------------------------
    - Uses frozen VAE encoders/decoder
    - Trains diffusion model in VAE's grasp latent space
    - Conditioning: z_pc from point cloud encoder
    
    PIPELINE DETAILS:
    =================
    
    Training Phase (forward):
    -------------------------
    Input: 
    - pc: [B, N, 3] point clouds (B batches, N points, 3D coordinates)
    - grasps: [B*G, 6/7] grasp poses (B*G total grasps, 6/7 DOF representation)
    
    Pipeline:
    1. VAE Encoding:
       - PC Encoding: pc [B, N, 3] → z_pc_cond [B, pc_latent_dims]
       - Grasp Encoding: grasps [B*G, 6/7] → (mu_h, logvar_h, z_h) [B*G, grasp_latent_dims]
       - z_pc_cond is repeated to match grasp batch size: [B*G, pc_latent_dims]
    
    2. Latent Diffusion:
       - Add noise to z_h: noised_z_h = z_h + σ * ε [B*G, 1, grasp_latent_dims]
       - Diffusion model predicts denoised latent: F_θ(noised_z_h, σ, z_pc_cond)
       - Compute denoising loss: L = ||F_θ(noised_z_h, σ, z_pc_cond) - z_h||²
    
    Output: denoising_loss (scalar)
    
    Inference Phase (generate_grasps):
    ----------------------------------
    Input:
    - xyz: [B, N, 3] point clouds for conditioning
    - num_grasps: int, number of grasps to generate per point cloud
    
    Pipeline:
    1. Point Cloud Encoding:
       - xyz [B, N, 3] → z_pc_cond [B, pc_latent_dims]
       - Repeat for each grasp: z_pc_cond [B*num_grasps, pc_latent_dims]
    
    2. Latent Diffusion Sampling:
       - Initialize noise: z₀ ~ N(0,I) [B*num_grasps, 1, grasp_latent_dims]
       - Iterative denoising: z₀ → z₁ → ... → z_T (clean latent)
       - Conditioning on z_pc_cond throughout the process
    
    3. VAE Decoding:
       - z_T [B*num_grasps, grasp_latent_dims] → grasps [B*num_grasps, 6/7]
       - Using frozen VAE decoder with point cloud conditioning
    
    Output: grasps [B*num_grasps, 6/7]
    
    DIMENSION FLOW SUMMARY:
    =======================
    - Point Clouds: [B, N, 3] → [B, pc_latent_dims] → [B*G, pc_latent_dims]
    - Grasps: [B*G, 6/7] → [B*G, grasp_latent_dims] → [B*G, 1, grasp_latent_dims] → [B*G, 6/7]
    - Diffusion operates in: [B*G, 1, grasp_latent_dims] space
    - Final output: [B*G, 6/7] where G = num_grasps per point cloud
    """
    
    def __init__(
        self,
        model,
        latent_in_features,
        diffusion_timesteps,
        diffusion_loss,
        beta_schedule="linear",
        noise_scheduler_type: str = "ddpm",
        is_conditioned=True,
        joint_training=False,
        denoising_loss_weight=1,
        variance_type="fixed_small",
        elucidated_diffusion=False,
        beta_start=5e-5,
        beta_end=5e-2,
    ) -> None:
        """Grasp Latent Diffusion Model

        Args:
            model (nn.Module): denoiser model (U-Net or similar)
                Expected interface:
                    def forward(self, x, *, t=t, z_cond=z_cond):
                        x: noisy latent [B, C, D] where D=latent_in_features  
                        t: timestep [B] or [B,1]
                        z_cond: point cloud conditioning [B, pc_latent_dims]
                    Returns: denoised prediction [B, C, D]

            latent_in_features (int): grasp latent space dimensions (D in [B, C, D])
            diffusion_timesteps (int): number of diffusion timesteps for training schedule
            diffusion_loss (str): loss type for diffusion training ("l1", "l2", "huber")
            beta_schedule (str, optional): noise schedule type. Defaults to "linear".
                Valid: ["linear", "scaled_linear", "squaredcos_cap_v2"]
            noise_scheduler_type (str, optional): scheduler for sampling. Defaults to "ddpm".
                Valid: ["ddpm", "ddim"]
            is_conditioned (bool, optional): whether to use point cloud conditioning. Defaults to True.
            joint_training (bool, optional): train VAE and diffusion jointly (deprecated). Defaults to False.
            denoising_loss_weight (int, optional): weight for denoising loss in joint training. Defaults to 1.
            variance_type (str, optional): variance type for noise addition. Defaults to "fixed_small".
                Valid: ["fixed_small", "fixed_small_log", "fixed_large", "fixed_large_log", "learned", "learned_range"]
            elucidated_diffusion (bool, optional): use EDM framework instead of DDPM. Defaults to False.
            beta_start (float, optional): starting noise level for DDPM. Defaults to 5e-5.
            beta_end (float, optional): ending noise level for DDPM. Defaults to 5e-2.
        """
        super().__init__()
        # VAE model will be set externally and frozen during diffusion training
        self.vae_model = None

        # Initialize diffusion model based on framework choice
        self.is_elucidated_diffusion = elucidated_diffusion
        if elucidated_diffusion:
            # EDM framework: more stable training, better sampling
            self.diffusion_model = ElucidatedDiffusion(
                net=model, seq_length=latent_in_features
            )
        else:
            # Standard DDPM framework
            self.diffusion_model = GaussianDiffusion1D(
                model=model,
                n_dims=latent_in_features,
                num_steps=diffusion_timesteps,
                loss_type=diffusion_loss,
                beta_schedule=beta_schedule,
                beta_start=beta_start,
                beta_end=beta_end,
                noise_scheduler_type=noise_scheduler_type,
                variance_type=variance_type,
            )

        # Training configuration
        self.is_conditioned = is_conditioned
        self.joint_training = joint_training  # TODO: Deprecate this
        self.loss_weight = denoising_loss_weight

        # VAE management flags
        self.is_vae_frozen = False

    @property
    def use_grasp_qualities(self):
        """Get whether grasp qualities are used for training
        
        Returns:
            bool: True if VAE decoder outputs grasp quality scores
        """
        return self.vae_model.use_grasp_qualities

    @property
    def scheduler_type(self):
        """Get Diffusion noise scheduler type
        
        Returns:
            str: scheduler type ("ddpm", "ddim", etc.)
        """
        return self.diffusion_model._noise_scheduler_type

    @property
    def _latent_loss_objects(self):
        """Hotfix to check where annealing should be applied
        
        Returns:
            list: VAE latent loss objects for annealing
        """
        return self.vae_model._latent_loss_objects

    def set_vae_model(self, vae_model):
        """Set and configure the VAE model for latent space operations

        Args:
            vae_model (nn.Module): trained VAE model with encoder/decoder
                Expected interface:
                - encode(pc, grasps) → ((mu_h, logvar_h, z_h), (mu_pc, logvar_pc, z_pc))
                - encode_pc(pc) → z_pc [B, pc_latent_dims]  
                - decoder(z_h, z_pc) → reconstructed_grasps [B, 6/7]
        """
        self.vae_model = vae_model
        return

    def load_vae_weights(self, state_dict):
        """Update VAE weights from checkpoint

        Args:
            state_dict (dict): VAE model state dictionary
        """
        self.vae_model.load_state_dict(state_dict, strict=True)
        return

    def set_inference_timesteps(self, num_inference_steps):
        """Set the number of inference steps for reverse diffusion sampler

        Args:
            num_inference_steps (int): number of denoising steps during sampling
                Fewer steps = faster sampling but potentially lower quality
        """
        self.diffusion_model.set_inference_timesteps(num_inference_steps)
        return

    def freeze_vae_model(self):
        """Freeze VAE parameters to prevent updates during diffusion training"""
        for param in self.vae_model.parameters():
            param.requires_grad = False
        self.vae_model.eval()
        self.is_vae_frozen = True
        return

    def forward(self, pc, grasps, compute_loss=None, **kwargs):
        """Training Forward Pass: Compute diffusion loss for point clouds and grasps

        Args:
            pc (torch.Tensor): input point clouds [B, N, 3]
                B = batch size, N = number of points, 3 = xyz coordinates
            grasps (torch.Tensor): input grasp poses [B*G, 6/7]  
                B*G = total grasps, 6/7 = pose representation (position + orientation [+ quality])
            compute_loss (bool, optional): legacy parameter for API compatibility. Defaults to None.

        Returns:
            tuple: (grasps_out, loss_dict)
                grasps_out: reconstructed grasps [B*G, 6/7] or None (if not joint training)
                loss_dict: Dict containing:
                    - loss: total training loss (scalar)
                    - denoising_loss: diffusion denoising loss (scalar)
                    - [VAE losses if joint_training=True]
                    
        Pipeline Details:
        1. VAE Encoding Phase:
           - pc [B, N, 3] → z_pc_cond [B, pc_latent_dims] (point cloud features)
           - grasps [B*G, 6/7] → z_h [B*G, grasp_latent_dims] (grasp latents)
           - z_pc_cond repeated to match grasp batch: [B*G, pc_latent_dims]
           
        2. Diffusion Training Phase:
           - z_h reshaped for diffusion: [B*G, 1, grasp_latent_dims]
           - Diffusion model adds noise and predicts clean latent
           - Loss computed between prediction and original z_h
           
        3. Optional Joint Training:
           - If enabled: also compute VAE reconstruction losses
           - Combine diffusion loss with VAE losses
        """
        # Ensure VAE is frozen (safety check for checkpoint resuming)
        if not self.is_vae_frozen:
            self.freeze_vae_model()
            warnings.warn("VAE model was frozen manually after loading")
            self.print_params_info()
            self.is_vae_frozen = True

        z_pc_cond = None
        # Phase 1: VAE Encoding
        # Point cloud and grasp encoding using frozen VAE encoders
        # pc: [B, N, 3] → z_pc_cond: [B, pc_latent_dims]  
        # grasps: [B*G, 6/7] → z_h: [B*G, grasp_latent_dims]
        (mu_h, logvar_h, z_h), (
            mu_pc,
            logvar_pc,
            z_pc_cond,
        ) = self.vae_model.encode(pc, grasps)

        # Phase 2: Latent Diffusion Training
        # Train diffusion model in VAE grasp latent space
        # z_h: [B*G, grasp_latent_dims] → [B*G, 1, grasp_latent_dims]
        # z_pc_cond: [B*G, pc_latent_dims] (conditioning)
        denoising_loss = self.diffusion_model(
            z_h.unsqueeze(1), z_cond=z_pc_cond, **kwargs
        )

        # Phase 3: Loss Computation
        if self.joint_training:
            # Joint training: combine diffusion loss with VAE losses
            denoising_loss *= self.loss_weight
            
            # VAE reconstruction: z_h [B*G, grasp_latent_dims] → grasps_out [B*G, 6/7]
            grasps_out = self.vae_model.decoder(z_h, z_pc_cond)
            
            # Compute VAE losses (reconstruction + KL divergence)
            loss_dict = self.vae_model._loss_fn(
                x_in=grasps,
                x_out=grasps_out,
                mu_h=mu_h,
                logvar_h=logvar_h,
                mu_pc=mu_pc,
                logvar_pc=logvar_pc,
            )
            
            # Add diffusion loss to total loss
            loss_dict.denoising_loss = denoising_loss
            loss_dict.loss = loss_dict.loss + denoising_loss

        else:
            # Standard diffusion training: only denoising loss
            grasps_out = None
            loss_dict = Dict(loss=denoising_loss, denoising_loss=denoising_loss)

        return grasps_out, loss_dict

    @torch.no_grad()
    def generate_grasps(self, xyz, num_grasps=10, return_intermediate=False, **kwargs):
        """Generation/Sampling: Generate grasps from point clouds using trained diffusion model

        Args:
            xyz (torch.Tensor): input point clouds [B, N, 3]
                B = batch size, N = number of points, 3 = xyz coordinates
            num_grasps (int, optional): number of grasps to generate per point cloud. Defaults to 10.
            return_intermediate (bool, optional): return intermediate diffusion states. Defaults to False.

        Returns:
            tuple: (generated_grasps, intermediate_outputs)
                generated_grasps: final grasp poses [B*num_grasps, 6/7]
                intermediate_outputs: list of intermediate states during diffusion sampling
                    - If return_intermediate=False: empty list []
                    - If return_intermediate=True: list of decoded grasps at sampling steps
                    
        Pipeline Details:
        1. Point Cloud Encoding:
           - xyz [B, N, 3] → z_pc_cond [B, pc_latent_dims]
           - Repeat conditioning: z_pc_cond [B*num_grasps, pc_latent_dims]
           
        2. Latent Diffusion Sampling:
           - Initialize pure noise: z₀ ~ N(0,I) [B*num_grasps, 1, grasp_latent_dims]
           - Iterative denoising with point cloud conditioning:
             z₀ → z₁ → z₂ → ... → z_clean [B*num_grasps, 1, grasp_latent_dims]
           - Uses trained diffusion model F_θ(z_t, t, z_pc_cond)
           
        3. VAE Decoding:  
           - z_clean [B*num_grasps, grasp_latent_dims] → grasps [B*num_grasps, 6/7]
           - Uses frozen VAE decoder with point cloud conditioning
           
        4. Optional Intermediate Decoding:
           - If return_intermediate=True: decode samples at multiple timesteps
           - Provides visualization of generation process
        """
        # Phase 1: Point Cloud Encoding  
        # Encode input point clouds to conditioning latents
        # xyz: [B, N, 3] → z_pc_cond: [B, pc_latent_dims]
        z_pc_cond = self.vae_model.encode_pc(xyz)

        # Phase 2: Prepare Batch Dimensions
        # Repeat point cloud conditioning for each generated grasp
        # z_pc_cond: [B, pc_latent_dims] → [B*num_grasps, pc_latent_dims]
        z_pc_cond = z_pc_cond.repeat_interleave(num_grasps, dim=0)

        # Phase 3: Latent Diffusion Sampling
        # Sample grasp latents using trained diffusion model
        # Sampling batch size: B*num_grasps (num_grasps per point cloud)
        sampling_batch_size = z_pc_cond.shape[0]
        
        # Diffusion sampling: noise → clean latents
        # out: [B*num_grasps, 1, grasp_latent_dims]
        # all_outs: list of intermediate states (if return_intermediate=True)
        out, all_outs = self.diffusion_model.sample(
            z_cond=z_pc_cond,
            batch_size=sampling_batch_size,
            return_all=return_intermediate,
            **kwargs
        )
        
        # Phase 4: VAE Decoding
        # Decode latent codes to grasp poses using frozen VAE decoder
        # out: [B*num_grasps, grasp_latent_dims] → [B*num_grasps, 6/7]
        out = self.vae_model.decoder(out.squeeze(-2), z_pc_cond)

        # Phase 5: Process Intermediate Outputs (if requested)
        if not return_intermediate:
            return (out, [])
        else:
            step_outs = []

            # Decode intermediate diffusion states (memory intensive)
            # Sample 50 timesteps uniformly across the diffusion process
            for idx in torch.linspace(0, len(all_outs) - 1, steps=50, dtype=torch.int):
                # Decode intermediate latent: [B*num_grasps, 1, grasp_latent_dims] → [B*num_grasps, 6/7]
                _out = self.vae_model.decoder(all_outs[idx].squeeze(-2), z_pc_cond)
                # Move to CPU to manage GPU memory
                inter_out = [_out_tensor.detach().cpu() for _out_tensor in _out]
                step_outs.append(inter_out)
                
        return out, step_outs

    def print_params_info(self):
        """Print model parameter statistics for debugging and monitoring"""

        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        non_trainable_params = sum(
            p.numel() for p in self.parameters() if not p.requires_grad
        )

        print("------------------------------------------------")
        print("Model Trainable Parameters: ", trainable_params)
        print("Model Non-Trainable Parameters: ", non_trainable_params)
        print("------------------------------------------------")
