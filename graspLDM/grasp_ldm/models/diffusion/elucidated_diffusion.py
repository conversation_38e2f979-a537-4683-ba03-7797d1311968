# Adapted from https://github.com/lucidrains/denoising-diffusion-pytorch/blob/main/denoising_diffusion_pytorch/elucidated_diffusion.py

import warnings
from math import sqrt

import torch
import torch.nn.functional as F
from einops import rearrange, reduce
from torch import nn
from tqdm import tqdm


# helpers
def exists(val):
    return val is not None


def default(val, d):
    if exists(val):
        return val
    return d() if callable(d) else d


# tensor helpers
def log(t, eps=1e-20):
    return torch.log(t.clamp(min=eps))


# normalization
def normalize_to_neg_one_to_one(img):
    return img * 2 - 1


def unnormalize_to_zero_to_one(t):
    return (t + 1) * 0.5


class ElucidatedDiffusion(nn.Module):
    """
    Elucidated Diffusion Model implementing the EDM framework from https://arxiv.org/abs/2206.00364
    
    PIPELINE OVERVIEW:
    ==================
    
    Training Phase (forward):
    -------------------------
    Input: clean data x [B, C, N], conditioning z_cond [B, ...]
    1. Sample noise level σ from log-normal distribution [B]
    2. Add noise: noised_x = x + σ * ε, where ε ~ N(0,I) [B, C, N]
    3. Preconditioned network prediction: denoised = F_θ(noised_x, σ, z_cond) [B, C, N]
    4. Compute MSE loss with σ-dependent weighting: L = weight(σ) * ||denoised - x||²
    Output: scalar loss
    
    Inference Phase (sample):
    -------------------------
    Input: conditioning z_cond [B, ...]
    1. Initialize with pure noise x₀ ~ σ_max * N(0,I) [batch_size, C, N]
    2. For each timestep i in noise schedule [σ_max, ..., σ_min, 0]:
       a. Apply stochastic noise injection (Langevin-like)
       b. Predict denoised sample via preconditioned network [B, C, N]
       c. Update sample using Heun's 2nd order method
    3. Return final denoised sample [B, C, N]
    
    DIMENSION FLOW:
    ===============
    - Input data: [B, C, N] where B=batch, C=channels, N=sequence_length
    - Conditioning: [B, ...] (variable dimensions based on conditioning type)
    - Noise levels: [B] -> [B, 1, 1] (broadcasted for element-wise operations)
    - Network output: [B, C, N] (same as input)
    - Final output: [B, C, N] (denoised samples)
    """
    
    def __init__(
        self,
        net,
        *,
        seq_length,
        channels=1,
        num_sample_steps=32,
        sigma_min=0.002,
        sigma_max=80,
        sigma_data=0.5,
        rho=7,
        P_mean=-1.2,
        P_std=1.2,
        S_churn=80,
        S_tmin=0.05,
        S_tmax=50,
        S_noise=1.003,
    ):
        """Elucidated Diffusion Model
        https://arxiv.org/abs/2206.00364

        Args:
            net (nn.Module): the denoising network that takes (x, time, z_cond) and outputs denoised prediction
            seq_length (int): sequence length N in data tensor [B, C, N]
            channels (int, optional): number of channels C in data tensor [B, C, N]. Defaults to 1.
            num_sample_steps (int, optional): number of sampling steps during inference. Defaults to 32.
            sigma_min (float, optional): minimum noise level in schedule. Defaults to 0.002.
            sigma_max (float, optional): maximum noise level in schedule. Defaults to 80.
            sigma_data (float, optional): data distribution std, used in preconditioning. Defaults to 0.5.
            rho (int, optional): controls sampling schedule density (Eq. 5 in paper). Defaults to 7.
            P_mean (float, optional): mean of log-normal noise distribution for training. Defaults to -1.2.
            P_std (float, optional): std of log-normal noise distribution for training. Defaults to 1.2.
            S_churn (int, optional): stochastic sampling parameter (Table 5). Defaults to 80.
            S_tmin (float, optional): stochastic sampling parameter (Table 5). Defaults to 0.05.
            S_tmax (float, optional): stochastic sampling parameter (Table 5). Defaults to 50.
            S_noise (float, optional): stochastic sampling parameter (Table 5). Defaults to 1.003.
        """
        super().__init__()
        assert net.random_or_learned_sinusoidal_cond
        self.self_condition = False  # net.self_condition

        self.net = net

        # Data dimensions - defines tensor shapes [B, C, N]
        self.channels = channels  # C dimension
        self.seq_length = seq_length  # N dimension

        # Noise schedule parameters
        self.sigma_min = sigma_min
        self.sigma_max = sigma_max
        self.sigma_data = sigma_data

        self.rho = rho

        # Training noise distribution parameters
        self.P_mean = P_mean
        self.P_std = P_std

        self.num_sample_steps = num_sample_steps  # otherwise known as N in the paper

        # Stochastic sampling parameters
        self.S_churn = S_churn
        self.S_tmin = S_tmin
        self.S_tmax = S_tmax
        self.S_noise = S_noise

    @property
    def device(self):
        return next(self.net.parameters()).device

    # Derived preconditioning parameters from Table 1 in EDM paper
    # These ensure optimal signal-to-noise ratio for network training

    def c_skip(self, sigma):
        """Skip connection weight: how much of original noisy input to preserve
        Input: sigma [B] or [B, 1, 1]
        Output: weight [same shape as sigma]
        """
        return (self.sigma_data**2) / (sigma**2 + self.sigma_data**2)

    def c_out(self, sigma):
        """Output scaling weight: scales the network prediction
        Input: sigma [B] or [B, 1, 1] 
        Output: weight [same shape as sigma]
        """
        return sigma * self.sigma_data * (self.sigma_data**2 + sigma**2) ** -0.5

    def c_in(self, sigma):
        """Input scaling weight: scales the noisy input to network
        Input: sigma [B] or [B, 1, 1]
        Output: weight [same shape as sigma]
        """
        return 1 * (sigma**2 + self.sigma_data**2) ** -0.5

    def c_noise(self, sigma):
        """Noise level embedding: converts sigma to time embedding
        Input: sigma [B]
        Output: time embedding [B]
        """
        return log(sigma) * 0.25

    def preconditioned_network_forward(
        self, noised_x, sigma, *, z_cond=None, self_cond=None, clamp=False
    ):
        """
        Preconditioned network forward pass implementing Equation (7) from EDM paper
        
        Input dimensions:
        - noised_x: [B, C, N] noisy input data
        - sigma: [B] or float, noise levels
        - z_cond: [B, ...] conditioning information (optional)
        - self_cond: [B, C, N] self-conditioning from previous step (optional)
        
        Output dimensions:
        - out: [B, C, N] denoised prediction
        
        Pipeline:
        1. Scale input by c_in(σ): noised_x * c_in → network input [B, C, N]
        2. Convert σ to time embedding: c_noise(σ) → time [B]
        3. Network prediction: net(scaled_input, time, z_cond) → net_out [B, C, N] 
        4. Preconditioned output: c_skip(σ) * noised_x + c_out(σ) * net_out → [B, C, N]
        """
        batch, device = noised_x.shape[0], noised_x.device

        # Ensure sigma is tensor with batch dimension [B]
        if isinstance(sigma, float):
            sigma = torch.full((batch,), sigma, device=device)

        # Reshape sigma for broadcasting: [B] → [B, 1, 1]
        padded_sigma = rearrange(sigma, "b -> b 1 1")

        # Network forward pass with preconditioning
        # Input scaling: [B, C, N] * [B, 1, 1] = [B, C, N]
        # Time embedding: [B] → [B]
        net_out = self.net(
            self.c_in(padded_sigma) * noised_x,  # Scaled input [B, C, N]
            time=self.c_noise(sigma),  # Time embedding [B]
            z_cond=z_cond,  # Conditioning [B, ...]
            x_self_cond=self_cond,  # Self-conditioning [B, C, N]
        )  # → net_out [B, C, N]

        # Preconditioned output: skip connection + scaled prediction
        # [B, 1, 1] * [B, C, N] + [B, 1, 1] * [B, C, N] = [B, C, N]
        out = self.c_skip(padded_sigma) * noised_x + self.c_out(padded_sigma) * net_out

        if clamp:
            out = out.clamp(-1.0, 1.0)

        return out  # [B, C, N]

    def sample_schedule(self, num_sample_steps=None):
        """
        Generate noise schedule for sampling following Equation (5) in EDM paper
        
        Input:
        - num_sample_steps: int, number of discretization steps
        
        Output:
        - sigmas: [num_sample_steps + 1] noise levels from σ_max to 0
        
        Schedule: σᵢ = (σ_max^(1/ρ) + i/(N-1) * (σ_min^(1/ρ) - σ_max^(1/ρ)))^ρ
        """
        num_sample_steps = default(num_sample_steps, self.num_sample_steps)

        N = num_sample_steps
        inv_rho = 1 / self.rho

        # Create step indices: [0, 1, 2, ..., N-1]
        steps = torch.arange(num_sample_steps, device=self.device, dtype=torch.float32)
        
        # Compute noise schedule: [num_sample_steps]
        sigmas = (
            self.sigma_max**inv_rho
            + steps / (N - 1) * (self.sigma_min**inv_rho - self.sigma_max**inv_rho)
        ) ** self.rho

        # Append final step σ=0: [num_sample_steps] → [num_sample_steps + 1]
        sigmas = F.pad(sigmas, (0, 1), value=0.0)  # last step is sigma value of 0.
        return sigmas  # [num_sample_steps + 1]

    def sample(self, **kwargs):
        """
        Main sampling interface - delegates to specific sampler
        
        Output:
        - x: [batch_size, C, N] final denoised samples
        - all_x: list of intermediate samples (if return_all=True)
        """
        if kwargs.pop("use_dpmpp"):
            x, all_x = self.sample_using_dpmpp(**kwargs)
        else:
            x, all_x = self.sample_normal(**kwargs)

        return x, all_x

    @torch.no_grad()
    def sample_normal(
        self,
        batch_size=16,
        z_cond=None,
        num_sample_steps=None,
        clamp=False,
        return_all=False,
    ):
        """
        Standard EDM sampling with Heun's 2nd order method
        
        Input:
        - batch_size: int, number of samples to generate
        - z_cond: [B, ...] conditioning information
        - num_sample_steps: int, discretization steps
        - clamp: bool, whether to clamp outputs to [-1, 1]
        - return_all: bool, whether to return intermediate samples
        
        Output:
        - x: [batch_size, C, N] final denoised samples
        - all_x: list of [batch_size, C, N] intermediate samples
        
        Sampling Pipeline:
        1. Initialize: x₀ ~ σ_max * N(0,I) [batch_size, C, N]
        2. For each timestep (σᵢ, σᵢ₊₁):
           a. Add stochastic noise: x̂ = x + √(σ̂² - σ²) * ε [batch_size, C, N]
           b. First order prediction: x' = x̂ + (σᵢ₊₁ - σ̂) * d [batch_size, C, N]
           c. Second order correction (if not final step) [batch_size, C, N]
        3. Return denoised samples [batch_size, C, N]
        """
        num_sample_steps = default(num_sample_steps, self.num_sample_steps)

        # Define sample shape: [batch_size, channels, seq_length]
        shape = (batch_size, self.channels, self.seq_length)

        # Get noise schedule: [num_sample_steps + 1]
        sigmas = self.sample_schedule(num_sample_steps)

        # Compute stochastic sampling parameters: [num_sample_steps + 1]
        gammas = torch.where(
            (sigmas >= self.S_tmin) & (sigmas <= self.S_tmax),
            min(self.S_churn / num_sample_steps, sqrt(2) - 1),
            0.0,
        )

        # Pair consecutive noise levels: [(σᵢ, σᵢ₊₁, γᵢ), ...]
        sigmas_and_gammas = list(zip(sigmas[:-1], sigmas[1:], gammas[:-1]))

        # Initialize with scaled noise: x₀ = σ_max * ε, where ε ~ N(0,I)
        init_sigma = sigmas[0]  # σ_max
        x = init_sigma * torch.randn(shape, device=self.device)  # [batch_size, C, N]

        # Storage for self-conditioning and intermediate results
        x_start = None
        all_x = [x]
        
        # Iterative denoising loop
        for sigma, sigma_next, gamma in tqdm(
            sigmas_and_gammas, desc="sampling time step"
        ):
            # Convert to scalars
            sigma, sigma_next, gamma = map(
                lambda t: t.item(), (sigma, sigma_next, gamma)
            )

            # Stochastic noise injection: ε ~ N(0,I) [batch_size, C, N]
            eps = self.S_noise * torch.randn(shape, device=self.device)

            # Adjusted noise level: σ̂ = σ + γσ
            sigma_hat = sigma + gamma * sigma
            
            # Add stochastic noise: x̂ = x + √(σ̂² - σ²) * ε [batch_size, C, N]
            x_hat = x + sqrt(sigma_hat**2 - sigma**2) * eps

            # Self-conditioning (currently disabled)
            self_cond = x_start if self.self_condition else None

            # First network evaluation: get denoised prediction [batch_size, C, N]
            model_output = self.preconditioned_network_forward(
                x_hat, sigma_hat, z_cond=z_cond, self_cond=self_cond, clamp=clamp
            )
            
            # Compute derivative: d = (x̂ - D_θ(x̂)) / σ̂ [batch_size, C, N]
            denoised_over_sigma = (x_hat - model_output) / sigma_hat

            # First order update: x' = x̂ + (σᵢ₊₁ - σ̂) * d [batch_size, C, N]
            x_next = x_hat + (sigma_next - sigma_hat) * denoised_over_sigma

            # Second order correction (Heun's method) - skip for final step
            if sigma_next != 0:
                self_cond = model_output if self.self_condition else None

                # Second network evaluation at x' [batch_size, C, N]
                model_output_next = self.preconditioned_network_forward(
                    x_next, sigma_next, z_cond=z_cond, self_cond=self_cond, clamp=clamp
                )
                
                # Second derivative: d' = (x' - D_θ(x')) / σᵢ₊₁ [batch_size, C, N]
                denoised_prime_over_sigma = (x_next - model_output_next) / sigma_next
                
                # Corrected update: x' = x̂ + 0.5 * (σᵢ₊₁ - σ̂) * (d + d') [batch_size, C, N]
                x_next = x_hat + 0.5 * (sigma_next - sigma_hat) * (
                    denoised_over_sigma + denoised_prime_over_sigma
                )

            # Update for next iteration
            x = x_next  # [batch_size, C, N]
            all_x += [x] if return_all else []
            x_start = model_output_next if sigma_next != 0 else model_output

        return x, all_x  # x: [batch_size, C, N], all_x: list of [batch_size, C, N]

    @torch.no_grad()
    def sample_using_dpmpp(
        self,
        batch_size=16,
        z_cond=None,
        num_sample_steps=20,
        clamp=False,
        return_all=False,
    ):
        """
        DPM++ sampling method - more efficient alternative sampler
        thanks to Katherine Crowson (https://github.com/crowsonkb) for figuring it all out!
        https://arxiv.org/abs/2211.01095
        
        Input/Output dimensions same as sample_normal()
        
        Pipeline:
        1. Initialize: x₀ ~ σ_max * N(0,I) [batch_size, C, N]  
        2. For each timestep: apply DPM++ multistep method [batch_size, C, N]
        3. Return denoised samples [batch_size, C, N]
        """
        if batch_size != z_cond.shape[0]:
            warnings.warn(
                f"The batch size for sample generation {batch_size} is different from conditioning batch_size {z_cond.shape[0]}."
                "\n This may be unreliable. If generation is being done for more than one pointcloud, batch_size should be (num_batch_pc*num_grasps_per_pc)."
            )

        device, num_sample_steps = self.device, default(
            num_sample_steps, self.num_sample_steps
        )

        # Get noise schedule: [num_sample_steps + 1]
        sigmas = self.sample_schedule(num_sample_steps)

        # Initialize with scaled noise: [batch_size, C, N]
        shape = (batch_size, self.channels, self.seq_length)
        x = sigmas[0] * torch.randn(shape, device=device)
        all_x = [x]

        # DPM++ coordinate transforms
        sigma_fn = lambda t: t.neg().exp()  # σ(t) = e^(-t)
        t_fn = lambda sigma: sigma.log().neg()  # t(σ) = -log(σ)

        old_denoised = None
        
        # DPM++ multistep sampling loop
        for i in tqdm(range(len(sigmas) - 1)):
            # Get denoised prediction: [batch_size, C, N]
            denoised = self.preconditioned_network_forward(
                x, sigmas[i].item(), z_cond=z_cond, clamp=clamp
            )
            
            # Time coordinates
            t, t_next = t_fn(sigmas[i]), t_fn(sigmas[i + 1])
            h = t_next - t

            # Multistep correction
            if not exists(old_denoised) or sigmas[i + 1] == 0:
                denoised_d = denoised  # [batch_size, C, N]
            else:
                h_last = t - t_fn(sigmas[i - 1])
                r = h_last / h
                gamma = -1 / (2 * r)
                # Linear combination of current and previous predictions
                denoised_d = (1 - gamma) * denoised + gamma * old_denoised  # [batch_size, C, N]

            # DPM++ update step: [batch_size, C, N]
            x = (sigma_fn(t_next) / sigma_fn(t)) * x - (-h).expm1() * denoised_d
            all_x += [x] if return_all else []
            old_denoised = denoised

        # x = x.clamp(-1.0, 1.0)
        # return unnormalize_to_zero_to_one(x)
        return x, all_x  # x: [batch_size, C, N], all_x: list of [batch_size, C, N]

    # Training methods

    def loss_weight(self, sigma):
        """
        Compute loss weighting function from EDM paper
        Input: sigma [B] noise levels
        Output: weights [B] for loss reweighting
        """
        return (sigma**2 + self.sigma_data**2) * (sigma * self.sigma_data) ** -2

    def noise_distribution(self, batch_size):
        """
        Sample noise levels from log-normal distribution for training
        Input: batch_size int
        Output: sigma [batch_size] sampled noise levels
        """
        return (
            self.P_mean + self.P_std * torch.randn((batch_size,), device=self.device)
        ).exp()

    def forward(self, x, *, z_cond=None):
        """
        Training forward pass - implements EDM training objective
        
        Input:
        - x: [B, C, N] clean training data
        - z_cond: [B, ...] conditioning information (optional)
        
        Output:
        - loss: scalar, weighted MSE loss for training
        
        Training Pipeline:
        1. Sample noise levels: σ ~ LogNormal(P_mean, P_std) [B]
        2. Add noise: noised_x = x + σ * ε, where ε ~ N(0,I) [B, C, N]
        3. Predict denoised: denoised = F_θ(noised_x, σ, z_cond) [B, C, N]
        4. Compute weighted MSE: loss = weight(σ) * ||denoised - x||² → scalar
        """
        b, c, n = x.shape
        assert (
            n == self.seq_length
        ), f"seq length must be {self.seq_length}, but got {n}"

        # Sample noise levels from log-normal distribution: [B]
        sigmas = self.noise_distribution(b)
        
        # Reshape for broadcasting: [B] → [B, 1, 1]
        padded_sigmas = rearrange(sigmas, "b -> b 1 1")

        # Sample noise: ε ~ N(0,I) [B, C, N]
        noise = torch.randn_like(x)

        # Add noise to clean data: noised_x = x + σ * ε [B, C, N]
        noised_x = x + padded_sigmas * noise  # alphas are 1. in the paper

        # Self-conditioning (currently not implemented)
        self_cond = None
        if self.self_condition:
            raise NotImplementedError

        # Get denoised prediction from preconditioned network: [B, C, N]
        denoised = self.preconditioned_network_forward(
            noised_x, sigmas, z_cond=z_cond, self_cond=self_cond
        )

        # Compute MSE loss: [B, C, N] → [B]
        losses = F.mse_loss(denoised, x, reduction="none")  # [B, C, N]
        losses = reduce(losses, "b ... -> b", "mean")  # [B, C, N] → [B]

        # Apply loss weighting: [B] * [B] = [B]
        losses = losses * self.loss_weight(sigmas)

        # Return mean loss: [B] → scalar
        return losses.mean()
