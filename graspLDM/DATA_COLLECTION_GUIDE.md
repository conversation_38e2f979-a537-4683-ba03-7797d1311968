# GraspLDM 自定义数据集采集指南

本文档将指导您如何为 `graspLDM` 项目采集和组织自己的抓取数据集，以便使用项目现有的训练流程进行模型训练。遵循本指南将确保您的数据与 `grasp_ldm/dataset/acronym/acronym_partial_pointclouds.py` 中的 `AcronymPartialPointclouds` 数据加载器兼容。

## 1. 硬件要求

-   **深度相机**: 建议使用能提供深度流和RGB流的相机，例如 **Intel RealSense D435i** 或类似设备。
-   **相机标定**: 您必须预先对相机进行标定，以获得其精确的内参矩阵。

## 2. 数据目录结构

您的自定义数据集应遵循以下目录结构。`grasp_ldm` 的数据加载器将从此结构中读取数据。

```
<your_dataset_name>/
├── train/
│   ├── scene_00000/
│   │   ├── 00000.npz
│   │   ├── scene_00000_cam_0.png
│   │   ├── scene_00000_cam_1.png
│   │   └── ... (更多来自不同视角的深度图)
│   ├── scene_00001/
│   │   ├── 00001.npz
│   │   ├── scene_00001_cam_0.png
│   │   └── ...
│   └── ... (更多场景)
├── test/
│   └── ... (测试集，结构与 train/ 相同)
└── camera.json
```

-   `<your_dataset_name>/`: 您的数据集根目录。
-   `train/`, `test/`: 训练集和测试集划分。
-   `scene_<id>/`: 单个场景的文件夹，`<id>` 为场景的唯一标识符（如 `00000`）。
-   `<id>.npz`: 与场景ID同名的 `.npz` 文件，包含该场景所有的抓取、相机位姿等元数据。
-   `scene_<id>_cam_<idx>.png`: 场景中的深度图，`<idx>` 为该场景下不同相机视角的索引。
-   `camera.json`: 存放相机内参的全局配置文件。

## 3. 文件格式详解

### 3.1. 相机内参 (`camera.json`)

此文件定义了您使用的深度相机的内部参数。格式应与 `grasp_ldm/dataset/cameras/camera_d435i_dummy.json` 一致。

```json
{
    "hfov": 87,
    "vfov": 58,
    "width": 640,
    "height": 480,
    "cameraMatrix": [
        [
            904.7,  // fx
            0,
            320.0   // cx
        ],
        [
            0,
            904.7,  // fy
            240.0   // cy
        ],
        [
            0,
            0,
            1
        ]
    ],
    "distCoeffs": [] // 畸变系数，如果您的图像已校正，则为空
}
```

### 3.2. 深度图 (`.png`)

-   **格式**: 必须是 **16位单通道** 的 PNG 图像。
-   **单位**: 像素值代表以特定单位计量的深度。数据加载器会使用一个缩放因子 `depth_px_scale`（默认为 `10000`）将其转换为米。换算关系为：
    `depth_in_meters = pixel_value / depth_px_scale`
    因此，如果您的相机输出以毫米为单位的深度图，`depth_px_scale` 应设为 `1000`。请确保保存的像素值与此关系对应。

### 3.3. 场景数据 (`.npz`)

这是最核心的数据文件，它是一个Numpy压缩归档文件，使用 `numpy.savez` 或 `numpy.savez_compressed` 创建。其中必须包含两个通过 `allow_pickle=True` 保存的 `dict` 对象：`grasps` 和 `renders`。

-   **`grasps` (dict)**: 包含场景中所有目标物体的抓取信息。
    -   `transforms`: `(N, 4, 4)` 的 `np.float32` 数组。表示 **N** 个抓取的位姿，以 **相对于世界坐标系** 的齐次变换矩阵存储。这个世界坐标系必须与您在 `renders['cam_poses']` 中使用的世界坐标系是 **同一个**。
    -   `success`: `(N,)` 的 `np.uint8` 或 `np.bool` 数组。表示每个抓取的成功与否，`1` 代表成功，`0` 代表失败。
    -   `qualities`: 一个 `dict`，可以为空 `{}`。用于存储额外的抓取质量度量，如果不需要，留空即可。

-   **`renders` (dict)**: 包含渲染（采集）该场景时与相机相关的信息。
    -   `cam_poses`: `(M, 4, 4)` 的 `np.float32` 数组。表示采集 **M** 张深度图时，每个视角对应的相机位姿。这是从 **世界坐标系到相机坐标系** 的齐次变换矩阵。

-   **`obj_path` (str, 可选)**: 指向物体三维网格模型（如 `.obj` 文件）的路径字符串。

## 4. 数据采集流程

1.  **场景设置**: 在物理或模拟环境中放置您希望采集抓取的物体。定义一个固定的、唯一的 **世界坐标系**。
2.  **相机放置与拍照**:
    -   将深度相机放置在不同位置和角度，对准场景。
    -   对于每个位置，记录相机相对于 **世界坐标系** 的位姿（`world_to_camera` 变换矩阵），这将是 `cam_poses` 数组的一行。
    -   从该位置拍摄一张深度图，并按照命名规则 `scene_<id>_cam_<idx>.png` 保存。
3.  **抓取标注**:
    -   **记录抓取**: 使用机械臂、模拟器或手动方式，记录一系列成功的和失败的抓取位姿。
    -   **确保世界坐标系**: 确保所有记录下来的抓取 `(4, 4)` 变换矩阵，都是 **相对于您在场景设置中定义的那个唯一的、固定的世界坐标系**。这个坐标系必须与您用来记录相机位姿的参考系完全一致。
    -   **打标签**: 为每个抓取位姿打上成功（1）或失败（0）的标签。
4.  **数据打包**:
    -   将一个场景中记录的所有抓取位姿矩阵堆叠成 `(N, 4, 4)` 的 `transforms` 数组。
    -   将所有对应的标签组成 `(N,)` 的 `success` 数组。
    -   将所有采集视角的相机位姿矩阵堆叠成 `(M, 4, 4)` 的 `cam_poses` 数组。
    -   使用下面的代码示例，将这些数据打包成 `.npz` 文件。

## 5. 代码示例：创建 `.npz` 文件

以下 Python 代码片段展示了如何创建一个符合规范的 `.npz` 文件。

```python
import numpy as np

def create_scene_npz(output_path, grasp_transforms, grasp_success_labels, camera_poses):
    """
    创建一个场景的 .npz 数据文件。

    Args:
        output_path (str): .npz 文件的输出路径 (例如: 'scene_00000/00000.npz')。
        grasp_transforms (np.ndarray): (N, 4, 4) 的抓取位姿数组。
        grasp_success_labels (np.ndarray): (N,) 的成功标签数组。
        camera_poses (np.ndarray): (M, 4, 4) 的相机位姿数组。
    """
    
    # 确保数据类型正确
    grasp_transforms = np.array(grasp_transforms, dtype=np.float32)
    grasp_success_labels = np.array(grasp_success_labels, dtype=np.uint8)
    camera_poses = np.array(camera_poses, dtype=np.float32)

    # 构建 grasps 字典
    grasps_dict = {
        'transforms': grasp_transforms,
        'success': grasp_success_labels,
        'qualities': {}  # 如果没有额外质量度量，则为空字典
    }

    # 构建 renders 字典
    renders_dict = {
        'cam_poses': camera_poses
    }
    
    # 使用 numpy.savez 保存，注意 allow_pickle=True
    np.savez(
        output_path,
        grasps=grasps_dict,
        renders=renders_dict,
        # obj_path='path/to/your/object.obj' # 可选
    )
    print(f"场景数据已保存到: {output_path}")

# --- 示例用法 ---
if __name__ == '__main__':
    # 假设这是您采集的数据
    num_grasps = 50
    num_cam_poses = 5

    # 模拟抓取数据 (相对于物体坐标系)
    mock_grasp_transforms = np.tile(np.eye(4), (num_grasps, 1, 1))
    mock_grasp_success = np.random.randint(0, 2, size=num_grasps)

    # 模拟相机位姿 (世界坐标系到相机坐标系)
    mock_camera_poses = np.tile(np.eye(4), (num_cam_poses, 1, 1))

    # 创建 .npz 文件
    create_scene_npz(
        '00000.npz',
        mock_grasp_transforms,
        mock_grasp_success,
        mock_camera_poses
    )
```

## 6. 集成到训练流程

完成数据采集和整理后，您需要修改项目中的训练配置文件（`.py` 文件），以告知训练器使用您的新数据集。

在您的配置文件中，找到 `data` 配置部分，并进行如下修改：

```python
# In your training config .py file

data = dict(
    train=dict(
        type='AcronymPartialPointclouds',  # 保持不变
        args=dict(
            data_root_dir='path/to/<your_dataset_name>',  # <--- 修改这里
            camera_json='path/to/<your_dataset_name>/camera.json', # <--- 修改这里
            split='train',
            # ... 其他参数可根据需要调整
            num_points_per_pc=1024,
            num_grasps_per_obj=50,
            depth_px_scale=10000, # 如果深度单位不是 1/10000 米，请修改
        )
    ),
    # val=dict(...) # 类似地配置验证集
) 
```

## 附录：理解完整的坐标系变换流程

为了帮助您更好地理解为什么数据需要以特定格式准备，以及它如何与模型的推理结果关联起来，这里概述了从数据准备到机器人执行的完整坐标系变换流程。

**这部分解释了我们在讨论中提到的"临时坐标系"是如何在幕后工作的。**

#### 1. 您的目标 (数据采集)

根据本指南，您需要为每个场景提供：
-   **深度图**: 从中可以生成在 **相机坐标系 (`Frame_Cam`)** 下的点云 `pc_cam`。
-   **相机位姿 `H_cam_world`**: 在 `.npz` 的 `renders['cam_poses']` 中。这是 **从世界坐标系到相机坐标系** 的变换矩阵。
-   **抓取位姿 `H_world_gripper`**: 在 `.npz` 的 `grasps['transforms']` 中，这是抓取器在 **世界坐标系 (`Frame_World`)** 下的位姿。

#### 2. 数据加载器的工作 (训练时)

当模型训练时，数据加载器 (`AcronymPartialPointclouds`) 会执行以下自动化预处理：

1.  **统一坐标系**: 将所有数据转换到相机坐标系下。它会使用 `H_cam_world` 和 `H_world_gripper` 来计算抓取在相机坐标系下的位姿： `H_cam_gripper = H_cam_world * H_world_gripper`。
2.  **创建临时坐标系**:
    -   计算点云 `pc_cam` 的几何中心（均值点） `pc_mean`。
    -   这个 `pc_mean` 定义了一个新的 **临时坐标系（PC-Mean Frame）** 的原点。该坐标系的姿态与相机坐标系平行。
3.  **中心化**:
    -   将点云和抓取位姿都转换到这个临时坐标系下，生成训练样本：
        -   `pc_centered = pc_cam - pc_mean`
        -   `grasp_cam_centered_translation = grasp_cam_translation - pc_mean`
4.  **标准化**: 最后，对中心化后的数据进行缩放，送入模型。

#### 3. 模型的学习与推理

-   **学习**: 模型学习的是中心化后的点云 `pc_centered` 和中心化后的抓取（的位移和旋转表示）之间的几何关系。
-   **推理**: 当您输入新的点云时，模型输出的正是这个"相对于临时坐标系"的抓取。

#### 4. 从推理到执行 (您需要做的)

这就是我们将模型输出应用到真实机器人上的步骤：

1.  **逆中心化与逆标准化**: 推理代码 (`inference.py`) 会自动处理这一步，将模型输出转换回标准的相机坐标系下的抓取 `H_cam_gripper`。
2.  **转换到基座坐标系**: 您需要使用来自手眼标定的相机在基座（世界）坐标系下的位姿 `H_base_cam`，来计算出机械臂可以执行的最终位姿：`H_base_gripper = H_base_cam * H_cam_gripper`。 **注意**：这里的`H_base_cam`是从相机坐标系到基座坐标系的变换，它通常是手眼标定得到的`H_cam_base`的逆矩阵。

通过这个流程，您可以看到，您在数据采集时提供的 `H_world_gripper`，经过一系列变换成为了模型的学习目标，并最终在推理后通过逆向变换被还原，用于机器人执行。