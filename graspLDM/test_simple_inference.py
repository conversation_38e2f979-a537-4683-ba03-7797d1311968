#!/usr/bin/env python3
"""
Test script for SimpleGraspLDMInference

This script tests the simplified inference interface without requiring
the full model to be loaded, focusing on the preprocessing pipeline.
"""

import sys
import os
import numpy as np
import torch

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        from tools.simple_inference import SimpleGraspLDMInference
        print("✅ SimpleGraspLDMInference imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_pointcloud_preparation():
    """Test point cloud preparation methods."""
    print("\n🧪 Testing point cloud preparation...")
    
    # Create a mock class with just the preparation methods
    class MockInference:
        def __init__(self):
            self.device = torch.device("cpu")
            self.num_points = 1024
        
        def _regularize_pointcloud(self, pointcloud, target_points):
            """Copy of the regularization method"""
            current_points = pointcloud.shape[0]
            
            if current_points < target_points:
                multiplier = max(target_points // current_points, 1)
                pc = pointcloud.repeat(multiplier, 1)
                
                num_extra = target_points - pc.shape[0]
                if num_extra > 0:
                    extra_indices = torch.randperm(pc.shape[0])[:num_extra]
                    extra_points = pc[extra_indices]
                    pc = torch.cat([pc, extra_points], dim=0)
                    
            elif current_points > target_points:
                indices = torch.randperm(current_points)[:target_points]
                pc = pointcloud[indices]
            else:
                pc = pointcloud
                
            return pc
        
        def _transform_pointcloud(self, pointcloud, camera_pose):
            """Copy of the transformation method"""
            if isinstance(camera_pose, np.ndarray):
                camera_pose = torch.from_numpy(camera_pose).float()
            
            if camera_pose.shape != (4, 4):
                raise ValueError(f"Camera pose must be [4, 4] matrix, got {camera_pose.shape}")
            
            ones = torch.ones(pointcloud.shape[0], 1, dtype=pointcloud.dtype, device=pointcloud.device)
            pc_homogeneous = torch.cat([pointcloud, ones], dim=1)
            pc_transformed = torch.matmul(pc_homogeneous, camera_pose.T)
            
            return pc_transformed[:, :3]
    
    mock = MockInference()
    
    # Test 1: Point cloud regularization
    print("  Testing point cloud regularization...")
    
    # Test upsampling (500 -> 1024 points)
    pc_small = torch.randn(500, 3)
    pc_reg = mock._regularize_pointcloud(pc_small, 1024)
    assert pc_reg.shape == (1024, 3), f"Expected (1024, 3), got {pc_reg.shape}"
    print(f"    ✅ Upsampling: {pc_small.shape} -> {pc_reg.shape}")
    
    # Test downsampling (2000 -> 1024 points)
    pc_large = torch.randn(2000, 3)
    pc_reg = mock._regularize_pointcloud(pc_large, 1024)
    assert pc_reg.shape == (1024, 3), f"Expected (1024, 3), got {pc_reg.shape}"
    print(f"    ✅ Downsampling: {pc_large.shape} -> {pc_reg.shape}")
    
    # Test exact size (1024 -> 1024 points)
    pc_exact = torch.randn(1024, 3)
    pc_reg = mock._regularize_pointcloud(pc_exact, 1024)
    assert pc_reg.shape == (1024, 3), f"Expected (1024, 3), got {pc_reg.shape}"
    print(f"    ✅ Exact size: {pc_exact.shape} -> {pc_reg.shape}")
    
    # Test 2: Camera pose transformation
    print("  Testing camera pose transformation...")
    
    pc = torch.randn(100, 3)
    
    # Identity transformation
    identity_pose = torch.eye(4)
    pc_transformed = mock._transform_pointcloud(pc, identity_pose)
    assert torch.allclose(pc, pc_transformed, atol=1e-6), "Identity transform failed"
    print("    ✅ Identity transformation")
    
    # Translation transformation
    translation_pose = torch.eye(4)
    translation_pose[:3, 3] = torch.tensor([1.0, 2.0, 3.0])
    pc_transformed = mock._transform_pointcloud(pc, translation_pose)
    expected = pc + torch.tensor([1.0, 2.0, 3.0])
    assert torch.allclose(pc_transformed, expected, atol=1e-6), "Translation transform failed"
    print("    ✅ Translation transformation")
    
    print("✅ Point cloud preparation tests passed!")
    return True

def test_preprocessing_pipeline():
    """Test the preprocessing pipeline logic."""
    print("\n🧪 Testing preprocessing pipeline...")
    
    # Mock normalization parameters (from the actual implementation)
    INPUT_PC_SHIFT = torch.zeros(3)
    INPUT_PC_SCALE = torch.ones(3) * 0.05
    INPUT_GRASP_SHIFT = torch.zeros(6)
    INPUT_GRASP_SCALE = torch.cat([
        torch.ones(3) * 0.05,  # translation scale
        torch.ones(3) * 0.5,   # rotation scale
    ])
    
    # Test point cloud preprocessing
    pc = torch.randn(1024, 3) * 0.1  # Small object scale
    
    # Step 1: Centering
    pc_mean = torch.mean(pc, dim=0)
    pc_centered = pc - pc_mean
    
    # Step 2: Normalization
    pc_normalized = (pc_centered - INPUT_PC_SHIFT) / INPUT_PC_SCALE
    
    # Step 3: Metadata preparation
    grasp_mean = INPUT_GRASP_SHIFT.clone()
    grasp_mean[:3] += pc_mean
    
    metas = {
        "pc_mean": INPUT_PC_SHIFT + pc_mean,
        "pc_std": INPUT_PC_SCALE,
        "grasp_mean": grasp_mean,
        "grasp_std": INPUT_GRASP_SCALE,
        "dataset_normalized": True,
    }
    
    # Verify shapes and values
    assert pc_normalized.shape == (1024, 3), f"Wrong normalized PC shape: {pc_normalized.shape}"
    assert torch.allclose(torch.mean(pc_normalized, dim=0), torch.zeros_like(pc_mean), atol=1e-5), "Centering failed"
    
    print(f"    ✅ Original PC mean: {pc_mean}")
    print(f"    ✅ Normalized PC mean: {torch.mean(pc_normalized, dim=0)}")
    print(f"    ✅ Metadata keys: {list(metas.keys())}")
    
    print("✅ Preprocessing pipeline tests passed!")
    return True

def test_synthetic_data_generation():
    """Test synthetic data generation for examples."""
    print("\n🧪 Testing synthetic data generation...")
    
    # Test sphere generation
    num_points = 2000
    theta = np.random.uniform(0, 2*np.pi, num_points)
    phi = np.random.uniform(0, np.pi, num_points)
    r = 0.1
    
    x = r * np.sin(phi) * np.cos(theta)
    y = r * np.sin(phi) * np.sin(theta)
    z = r * np.cos(phi)
    
    sphere_pc = np.column_stack([x, y, z])
    
    assert sphere_pc.shape == (num_points, 3), f"Wrong sphere shape: {sphere_pc.shape}"
    
    # Check if points are approximately on sphere surface
    distances = np.linalg.norm(sphere_pc, axis=1)
    assert np.allclose(distances, r, atol=1e-10), "Points not on sphere surface"
    
    print(f"    ✅ Sphere generation: {sphere_pc.shape}, radius={r}")
    
    # Test camera pose generation
    translation = [0, 0, 0.5]
    rotation_deg = [0, 0, 45]
    
    rx, ry, rz = np.radians(rotation_deg)
    
    Rx = np.array([[1, 0, 0],
                   [0, np.cos(rx), -np.sin(rx)],
                   [0, np.sin(rx), np.cos(rx)]])
    
    Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                   [0, 1, 0],
                   [-np.sin(ry), 0, np.cos(ry)]])
    
    Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                   [np.sin(rz), np.cos(rz), 0],
                   [0, 0, 1]])
    
    R = Rz @ Ry @ Rx
    
    T = np.eye(4)
    T[:3, :3] = R
    T[:3, 3] = translation
    
    assert T.shape == (4, 4), f"Wrong camera pose shape: {T.shape}"
    assert np.allclose(T[3, :], [0, 0, 0, 1]), "Wrong homogeneous row"
    
    print(f"    ✅ Camera pose generation: {T.shape}")
    
    print("✅ Synthetic data generation tests passed!")
    return True

def main():
    """Run all tests."""
    print("🚀 Testing SimpleGraspLDMInference Implementation")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_pointcloud_preparation,
        test_preprocessing_pipeline,
        test_synthetic_data_generation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The simplified inference interface is ready to use.")
        print("\nNext steps:")
        print("1. Ensure you have model checkpoints in the correct directory")
        print("2. Run: python examples/simple_inference_example.py --exp_path <your_exp_path>")
        print("3. Try with your own point cloud data")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
